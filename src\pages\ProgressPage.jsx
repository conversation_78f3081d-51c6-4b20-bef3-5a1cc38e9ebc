import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { <PERSON>, Badge, Button } from '../components/ui';
import { 
  TrendingUp, 
  Target, 
  DollarSign, 
  Calendar, 
  Award, 
  BarChart3,
  Clock,
  CheckCircle,
  Star,
  ArrowUp,
  ArrowDown,
  Eye
} from 'lucide-react';

const ProgressPage = () => {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Mock data for progress tracking
  const stats = {
    totalEarnings: 12450,
    activeHustles: 3,
    completedGoals: 8,
    successRate: 85
  };

  const recentActivity = [
    {
      id: 1,
      type: 'goal_completed',
      title: 'Completed "Launch Online Store"',
      date: '2024-01-15',
      earnings: 2500,
      status: 'success'
    },
    {
      id: 2,
      type: 'hustle_started',
      title: 'Started "Freelance Writing"',
      date: '2024-01-12',
      earnings: 0,
      status: 'active'
    },
    {
      id: 3,
      type: 'milestone',
      title: 'Reached $10K total earnings',
      date: '2024-01-10',
      earnings: 10000,
      status: 'milestone'
    }
  ];

  const goals = [
    {
      id: 1,
      title: 'Reach $15K in earnings',
      current: 12450,
      target: 15000,
      deadline: '2024-02-28',
      status: 'active'
    },
    {
      id: 2,
      title: 'Complete 5 side hustles',
      current: 3,
      target: 5,
      deadline: '2024-03-31',
      status: 'active'
    },
    {
      id: 3,
      title: 'Build email list of 1000',
      current: 750,
      target: 1000,
      deadline: '2024-02-15',
      status: 'active'
    }
  ];

  const achievements = [
    { id: 1, title: 'First Sale', description: 'Made your first $100', earned: true },
    { id: 2, title: 'Consistent Earner', description: 'Earned for 30 days straight', earned: true },
    { id: 3, title: 'Goal Crusher', description: 'Completed 5 goals', earned: true },
    { id: 4, title: 'High Roller', description: 'Earned $10K total', earned: true },
    { id: 5, title: 'Diversified', description: 'Active in 5 different hustles', earned: false },
    { id: 6, title: 'Mentor', description: 'Helped 10 other users', earned: false }
  ];

  const getProgressPercentage = (current, target) => {
    return Math.min((current / target) * 100, 100);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Progress Dashboard</h1>
          <p className="text-gray-600 mt-2">Track your hustle journey and achievements</p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <Card.Content className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                  <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalEarnings)}</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <ArrowUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-green-500">+12%</span>
                <span className="text-gray-500 ml-1">from last month</span>
              </div>
            </Card.Content>
          </Card>

          <Card>
            <Card.Content className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Hustles</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.activeHustles}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Target className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <ArrowUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-green-500">+1</span>
                <span className="text-gray-500 ml-1">this week</span>
              </div>
            </Card.Content>
          </Card>

          <Card>
            <Card.Content className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed Goals</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.completedGoals}</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <ArrowUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-green-500">+2</span>
                <span className="text-gray-500 ml-1">this month</span>
              </div>
            </Card.Content>
          </Card>

          <Card>
            <Card.Content className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.successRate}%</p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-orange-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <ArrowUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-green-500">+5%</span>
                <span className="text-gray-500 ml-1">from last month</span>
              </div>
            </Card.Content>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Current Goals */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center">
                <Target className="w-5 h-5 mr-2" />
                Current Goals
              </Card.Title>
            </Card.Header>
            <Card.Content className="space-y-4">
              {goals.map((goal) => (
                <div key={goal.id} className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{goal.title}</h4>
                    <Badge variant={goal.status === 'active' ? 'default' : 'secondary'}>
                      {goal.status}
                    </Badge>
                  </div>
                  <div className="mb-2">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>{typeof goal.current === 'number' && goal.title.includes('$') ? formatCurrency(goal.current) : goal.current}</span>
                      <span>{typeof goal.target === 'number' && goal.title.includes('$') ? formatCurrency(goal.target) : goal.target}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${getProgressPercentage(goal.current, goal.target)}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="w-4 h-4 mr-1" />
                    Due: {new Date(goal.deadline).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </Card.Content>
          </Card>

          {/* Recent Activity */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                Recent Activity
              </Card.Title>
            </Card.Header>
            <Card.Content className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    activity.status === 'success' ? 'bg-green-100' :
                    activity.status === 'active' ? 'bg-blue-100' : 'bg-yellow-100'
                  }`}>
                    {activity.status === 'success' ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : activity.status === 'active' ? (
                      <Clock className="w-4 h-4 text-blue-600" />
                    ) : (
                      <Star className="w-4 h-4 text-yellow-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{activity.title}</p>
                    <p className="text-sm text-gray-600">{new Date(activity.date).toLocaleDateString()}</p>
                    {activity.earnings > 0 && (
                      <p className="text-sm font-medium text-green-600">
                        +{formatCurrency(activity.earnings)}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </Card.Content>
          </Card>
        </div>

        {/* Achievements */}
        <Card className="mt-8">
          <Card.Header>
            <Card.Title className="flex items-center">
              <Award className="w-5 h-5 mr-2" />
              Achievements
            </Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.map((achievement) => (
                <div
                  key={achievement.id}
                  className={`p-4 border rounded-lg transition-all ${
                    achievement.earned
                      ? 'border-yellow-300 bg-yellow-50'
                      : 'border-gray-200 bg-gray-50 opacity-60'
                  }`}
                >
                  <div className="flex items-center mb-2">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                      achievement.earned ? 'bg-yellow-100' : 'bg-gray-200'
                    }`}>
                      <Award className={`w-4 h-4 ${
                        achievement.earned ? 'text-yellow-600' : 'text-gray-400'
                      }`} />
                    </div>
                    <h4 className={`font-medium ${
                      achievement.earned ? 'text-gray-900' : 'text-gray-500'
                    }`}>
                      {achievement.title}
                    </h4>
                  </div>
                  <p className={`text-sm ${
                    achievement.earned ? 'text-gray-600' : 'text-gray-400'
                  }`}>
                    {achievement.description}
                  </p>
                </div>
              ))}
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  );
};

export default ProgressPage;
