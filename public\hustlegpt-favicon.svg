<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="16" cy="16" r="16" fill="url(#gradient)" />
  
  <!-- AI Brain/Circuit Pattern -->
  <g stroke="#ffffff" stroke-width="1.5" fill="none" opacity="0.9">
    <!-- Central Hub -->
    <circle cx="16" cy="16" r="3" fill="#ffffff" />
    
    <!-- Neural Network Lines -->
    <path d="M16 13 L12 8 M16 13 L20 8 M16 19 L12 24 M16 19 L20 24" stroke-linecap="round" />
    <path d="M13 16 L8 12 M13 16 L8 20 M19 16 L24 12 M19 16 L24 20" stroke-linecap="round" />
    
    <!-- Connection Nodes -->
    <circle cx="12" cy="8" r="1.5" fill="#ffffff" />
    <circle cx="20" cy="8" r="1.5" fill="#ffffff" />
    <circle cx="12" cy="24" r="1.5" fill="#ffffff" />
    <circle cx="20" cy="24" r="1.5" fill="#ffffff" />
    <circle cx="8" cy="12" r="1.5" fill="#ffffff" />
    <circle cx="8" cy="20" r="1.5" fill="#ffffff" />
    <circle cx="24" cy="12" r="1.5" fill="#ffffff" />
    <circle cx="24" cy="20" r="1.5" fill="#ffffff" />
  </g>
  
  <!-- Dollar Sign Overlay -->
  <g fill="#ffffff" opacity="0.8">
    <path d="M15 10 L15 8 L17 8 L17 10 M15 22 L15 24 L17 24 L17 22" stroke="#ffffff" stroke-width="1" />
    <path d="M13 12 C13 11 14 10 16 10 C18 10 19 11 19 12 C19 13 18 13 16 14 C14 15 13 15 13 16 C13 17 14 18 16 18 C18 18 19 17 19 16" 
          stroke="#ffffff" stroke-width="1.5" fill="none" stroke-linecap="round" />
  </g>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
