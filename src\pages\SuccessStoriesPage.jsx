import { useState, useEffect } from 'react';
import { Star, DollarSign, Clock, TrendingUp, Users, Plus, Filter, Quote, MapPin, Calendar, CheckCircle, ArrowLeft, ExternalLink, Play, Pause, Square, Volume2, VolumeX } from 'lucide-react';
import { But<PERSON>, Card, Badge, Modal, Input, Textarea } from '../components/ui';
import { LoadingSpinner } from '../components/common';
import { successStoriesAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import Chatbot from '../components/chatbot/Chatbot';

const SuccessStoriesPage = () => {
  const [stories, setStories] = useState([]);
  const [featuredStories, setFeaturedStories] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [selectedStory, setSelectedStory] = useState(null);
  const [showFullStory, setShowFullStory] = useState(false);
  const [filters, setFilters] = useState({
    hustle: 'all',
    earnings: 'all',
    timeframe: 'all'
  });

  // Text-to-Speech state
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentUtterance, setCurrentUtterance] = useState(null);
  const [speechProgress, setSpeechProgress] = useState(0);
  const [speechRate, setSpeechRate] = useState(1);
  const [speechVolume, setSpeechVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [currentSentence, setCurrentSentence] = useState('');

  const { isAuthenticated } = useAuth();

  useEffect(() => {
    loadSuccessStories();
  }, []);

  // Cleanup TTS on unmount
  useEffect(() => {
    return () => {
      if (currentUtterance) {
        speechSynthesis.cancel();
      }
    };
  }, [currentUtterance]);

  const loadSuccessStories = async () => {
    setIsLoading(true);
    try {
      const [allStories, featured] = await Promise.all([
        successStoriesAPI.getAll(),
        successStoriesAPI.getFeatured()
      ]);
      setStories(allStories);
      setFeaturedStories(featured);
    } catch (error) {
      console.error('Error loading success stories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReadFullStory = (story) => {
    setSelectedStory(story);
    setShowFullStory(true);
  };

  const handleCloseFullStory = () => {
    setShowFullStory(false);
    setSelectedStory(null);
    // Stop any playing speech when closing modal
    if (currentUtterance) {
      speechSynthesis.cancel();
      setIsPlaying(false);
      setIsPaused(false);
      setSpeechProgress(0);
      setCurrentUtterance(null);
      setCurrentSentence('');
    }
  };

  // Text-to-Speech Functions
  const getStoryTextContent = (story) => {
    if (!story) return '';

    // Extract text from HTML content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = story.fullStory || '';
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Combine name, hustle, story summary, and full content
    return `${story.name}'s Success Story. ${story.hustle}. ${story.story}. ${textContent}`;
  };

  const startSpeech = () => {
    if (!selectedStory) return;

    // Stop any existing speech
    if (currentUtterance) {
      speechSynthesis.cancel();
    }

    const textToSpeak = getStoryTextContent(selectedStory);
    const utterance = new SpeechSynthesisUtterance(textToSpeak);

    // Configure speech settings
    utterance.rate = speechRate;
    utterance.volume = isMuted ? 0 : speechVolume;
    utterance.pitch = 1;

    // Event handlers
    utterance.onstart = () => {
      setIsPlaying(true);
      setIsPaused(false);
      setSpeechProgress(0);
    };

    utterance.onend = () => {
      setIsPlaying(false);
      setIsPaused(false);
      setSpeechProgress(100);
      setCurrentUtterance(null);
      setCurrentSentence('');
    };

    utterance.onerror = () => {
      setIsPlaying(false);
      setIsPaused(false);
      setCurrentUtterance(null);
      setCurrentSentence('');
    };

    utterance.onboundary = (event) => {
      if (event.name === 'sentence') {
        const progress = (event.charIndex / textToSpeak.length) * 100;
        setSpeechProgress(progress);

        // Extract current sentence for display
        const sentences = textToSpeak.split(/[.!?]+/);
        let charCount = 0;
        for (let sentence of sentences) {
          charCount += sentence.length + 1;
          if (charCount > event.charIndex) {
            setCurrentSentence(sentence.trim());
            break;
          }
        }
      }
    };

    setCurrentUtterance(utterance);
    speechSynthesis.speak(utterance);
  };

  const pauseSpeech = () => {
    if (speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
      setIsPaused(true);
    }
  };

  const resumeSpeech = () => {
    if (speechSynthesis.paused) {
      speechSynthesis.resume();
      setIsPaused(false);
    }
  };

  const stopSpeech = () => {
    speechSynthesis.cancel();
    setIsPlaying(false);
    setIsPaused(false);
    setSpeechProgress(0);
    setCurrentUtterance(null);
    setCurrentSentence('');
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    if (currentUtterance) {
      currentUtterance.volume = isMuted ? speechVolume : 0;
    }
  };

  const handleRateChange = (newRate) => {
    setSpeechRate(newRate);
    if (isPlaying && currentUtterance) {
      // Restart with new rate
      const wasPlaying = isPlaying;
      stopSpeech();
      if (wasPlaying) {
        setTimeout(startSpeech, 100);
      }
    }
  };

  const filteredStories = stories.filter(story => {
    if (filters.hustle !== 'all' && !story.hustle.toLowerCase().includes(filters.hustle)) return false;
    if (filters.earnings !== 'all') {
      const earnings = parseInt(story.earnings.replace(/[^0-9]/g, ''));
      if (filters.earnings === 'under1000' && earnings >= 1000) return false;
      if (filters.earnings === '1000-5000' && (earnings < 1000 || earnings > 5000)) return false;
      if (filters.earnings === 'over5000' && earnings <= 5000) return false;
    }
    return true;
  });

  if (isLoading) {
    return <LoadingSpinner fullScreen text="Loading success stories..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Success Stories
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            Real people, real results. Get inspired by entrepreneurs who turned their side hustles into success
          </p>
          {isAuthenticated && (
            <Button
              onClick={() => setShowSubmitModal(true)}
              leftIcon={<Plus className="w-5 h-5" />}
            >
              Share Your Story
            </Button>
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card className="text-center p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">500+</div>
            <div className="text-gray-600">Success Stories</div>
          </Card>
          <Card className="text-center p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-4">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">$10M+</div>
            <div className="text-gray-600">Total Earned</div>
          </Card>
          <Card className="text-center p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-4">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">95%</div>
            <div className="text-gray-600">Success Rate</div>
          </Card>
          <Card className="text-center p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mb-4">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">6 months</div>
            <div className="text-gray-600">Avg. to Success</div>
          </Card>
        </div>

        {/* Filters */}
        <Card className="p-6 mb-8">
          <div className="flex items-center gap-4">
            <Filter className="w-5 h-5 text-gray-600" />
            <div className="flex flex-wrap gap-4">
              <select
                value={filters.hustle}
                onChange={(e) => setFilters({...filters, hustle: e.target.value})}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Hustles</option>
                <option value="writing">Writing</option>
                <option value="tutoring">Tutoring</option>
                <option value="ecommerce">E-commerce</option>
                <option value="design">Design</option>
                <option value="marketing">Marketing</option>
              </select>
              <select
                value={filters.earnings}
                onChange={(e) => setFilters({...filters, earnings: e.target.value})}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Earnings</option>
                <option value="under1000">Under $1,000/month</option>
                <option value="1000-5000">$1,000 - $5,000/month</option>
                <option value="over5000">Over $5,000/month</option>
              </select>
            </div>
          </div>
        </Card>

        {/* Featured Stories */}
        {featuredStories.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Star className="w-6 h-6 text-yellow-500 mr-2" />
              Featured Success Stories
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredStories.map((story) => (
                <Card key={story.id} className="hover:shadow-lg transition-shadow overflow-hidden">
                  {/* Cover Image */}
                  <div className="aspect-video bg-gray-200 overflow-hidden">
                    {story.coverImage ? (
                      <img
                        src={story.coverImage}
                        alt={`${story.name}'s success story`}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-2xl">
                        {story.name.charAt(0)}
                      </div>
                    )}
                  </div>

                  <Card.Content className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 rounded-full overflow-hidden mr-4 border-2 border-gray-200">
                        {story.avatar ? (
                          <img
                            src={story.avatar}
                            alt={story.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg">
                            {story.name.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold text-gray-900">{story.name}</h3>
                          {story.verified && (
                            <CheckCircle className="w-4 h-4 text-blue-500" />
                          )}
                        </div>
                        <p className="text-sm text-gray-600">{story.hustle}</p>
                        {story.location && (
                          <div className="flex items-center text-xs text-gray-500 mt-1">
                            <MapPin className="w-3 h-3 mr-1" />
                            {story.location}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center mb-4">
                      <div className="flex mr-2">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600">
                        {story.socialProof?.clientRating || '5.0'} ({story.socialProof?.testimonials || 0} reviews)
                      </span>
                    </div>

                    <blockquote className="text-gray-700 mb-4 italic relative">
                      <Quote className="w-4 h-4 text-gray-400 absolute -top-1 -left-1" />
                      <span className="ml-3">{story.story}</span>
                    </blockquote>

                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div>
                        <span className="text-gray-500">Earnings:</span>
                        <div className="font-semibold text-green-600">{story.earnings}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Timeframe:</span>
                        <div className="font-semibold">{story.timeframe}</div>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        className="flex-1"
                        onClick={() => handleReadFullStory(story)}
                      >
                        Read Full Story
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="px-3"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                    </div>
                  </Card.Content>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* All Stories */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            All Success Stories ({filteredStories.length})
          </h2>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredStories.map((story) => (
            <Card key={story.id} className="hover:shadow-lg transition-shadow">
              <Card.Content className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full overflow-hidden mr-4 border-2 border-gray-200">
                    {story.avatar ? (
                      <img
                        src={story.avatar}
                        alt={story.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center text-white font-bold text-lg">
                        {story.name.charAt(0)}
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-gray-900">{story.name}</h3>
                      {story.verified && (
                        <CheckCircle className="w-4 h-4 text-blue-500" />
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{story.hustle}</p>
                    {story.location && (
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <MapPin className="w-3 h-3 mr-1" />
                        {story.location}
                      </div>
                    )}
                  </div>
                </div>

                <p className="text-gray-700 text-sm mb-4 line-clamp-3">{story.story}</p>

                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <span className="text-gray-500">Earnings:</span>
                    <div className="font-semibold text-green-600">{story.earnings}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Timeframe:</span>
                    <div className="font-semibold">{story.timeframe}</div>
                  </div>
                </div>

                <Button
                  size="sm"
                  fullWidth
                  onClick={() => handleReadFullStory(story)}
                >
                  Read Full Story
                </Button>
              </Card.Content>
            </Card>
          ))}
        </div>

        {filteredStories.length === 0 && (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
              <Users className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No stories found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your filters</p>
            <Button onClick={() => setFilters({ hustle: 'all', earnings: 'all', timeframe: 'all' })}>
              Clear Filters
            </Button>
          </div>
        )}

        {/* Submit Story Modal */}
        <SubmitStoryModal
          isOpen={showSubmitModal}
          onClose={() => setShowSubmitModal(false)}
        />

        {/* Full Story Modal */}
        {showFullStory && selectedStory && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              {/* Modal Header */}
              <div className="sticky top-0 bg-white border-b border-gray-200 p-6 z-10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <button
                      onClick={handleCloseFullStory}
                      className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <ArrowLeft className="w-5 h-5" />
                    </button>
                    <div className="flex items-center">
                      <div className="w-12 h-12 rounded-full overflow-hidden mr-4 border-2 border-gray-200">
                        {selectedStory.avatar ? (
                          <img
                            src={selectedStory.avatar}
                            alt={selectedStory.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg">
                            {selectedStory.name.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h2 className="text-xl font-bold text-gray-900">{selectedStory.name}</h2>
                          {selectedStory.verified && (
                            <CheckCircle className="w-5 h-5 text-blue-500" />
                          )}
                        </div>
                        <p className="text-gray-600">{selectedStory.hustle}</p>
                        <div className="flex items-center text-sm text-gray-500 mt-1">
                          <MapPin className="w-4 h-4 mr-1" />
                          {selectedStory.location}
                          <span className="mx-2">•</span>
                          <Calendar className="w-4 h-4 mr-1" />
                          Age {selectedStory.age}
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={handleCloseFullStory}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Cover Image */}
              {selectedStory.coverImage && (
                <div className="aspect-video bg-gray-200 overflow-hidden">
                  <img
                    src={selectedStory.coverImage}
                    alt={`${selectedStory.name}'s success story`}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              {/* Text-to-Speech Controls */}
              <div className="p-6 pb-0">
                <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Volume2 className="w-5 h-5 text-gray-600" />
                      <h3 className="text-lg font-semibold text-gray-900">Listen to Success Story</h3>
                      {isPlaying && (
                        <div className="flex items-center gap-2 text-sm text-blue-600">
                          <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                          <span>Playing</span>
                        </div>
                      )}
                    </div>

                    {/* Volume and Rate Controls */}
                    <div className="flex items-center gap-3">
                      <button
                        onClick={toggleMute}
                        className="p-2 hover:bg-gray-200 rounded-full transition-colors"
                        title={isMuted ? 'Unmute' : 'Mute'}
                      >
                        {isMuted ? (
                          <VolumeX className="w-4 h-4 text-gray-600" />
                        ) : (
                          <Volume2 className="w-4 h-4 text-gray-600" />
                        )}
                      </button>

                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">Speed:</span>
                        <select
                          value={speechRate}
                          onChange={(e) => handleRateChange(parseFloat(e.target.value))}
                          className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value={0.5}>0.5x</option>
                          <option value={0.75}>0.75x</option>
                          <option value={1}>1x</option>
                          <option value={1.25}>1.25x</option>
                          <option value={1.5}>1.5x</option>
                          <option value={2}>2x</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Main Controls */}
                  <div className="flex items-center gap-3 mb-4">
                    {!isPlaying ? (
                      <Button
                        onClick={startSpeech}
                        className="flex items-center gap-2"
                        size="sm"
                      >
                        <Play className="w-4 h-4" />
                        Play Story
                      </Button>
                    ) : (
                      <div className="flex items-center gap-2">
                        {!isPaused ? (
                          <Button
                            onClick={pauseSpeech}
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-2"
                          >
                            <Pause className="w-4 h-4" />
                            Pause
                          </Button>
                        ) : (
                          <Button
                            onClick={resumeSpeech}
                            className="flex items-center gap-2"
                            size="sm"
                          >
                            <Play className="w-4 h-4" />
                            Resume
                          </Button>
                        )}

                        <Button
                          onClick={stopSpeech}
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-2"
                        >
                          <Square className="w-4 h-4" />
                          Stop
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* Progress Bar */}
                  {isPlaying && (
                    <div className="mb-4">
                      <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>Progress</span>
                        <span>{Math.round(speechProgress)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${speechProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Current Sentence Display */}
                  {isPlaying && currentSentence && (
                    <div className="p-3 bg-white rounded border border-gray-200">
                      <div className="text-sm text-gray-600 mb-1">Currently reading:</div>
                      <div className="text-gray-800 italic">"{currentSentence}"</div>
                    </div>
                  )}

                  {/* Reading Info */}
                  <div className="text-sm text-gray-600 mt-3">
                    <div className="flex items-center gap-4">
                      <span>📖 Story length: {selectedStory.timeframe} journey</span>
                      <span>🎧 Audio length: ~{Math.ceil(getStoryTextContent(selectedStory).length / 200)} minutes</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Story Content */}
              <div className="p-6 pt-0">
                {/* Success Metrics */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 p-4 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{selectedStory.earnings}</div>
                    <div className="text-sm text-gray-600">Monthly Earnings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedStory.timeframe}</div>
                    <div className="text-sm text-gray-600">Time to Success</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{selectedStory.hoursPerWeek}</div>
                    <div className="text-sm text-gray-600">Hours per Week</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{selectedStory.clients}</div>
                    <div className="text-sm text-gray-600">Active Clients</div>
                  </div>
                </div>

                {/* Social Proof */}
                <div className="flex items-center justify-center gap-6 mb-8 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center">
                    <Star className="w-5 h-5 text-yellow-400 fill-current mr-1" />
                    <span className="font-semibold">{selectedStory.socialProof?.clientRating}</span>
                    <span className="text-gray-600 ml-1">rating</span>
                  </div>
                  <div className="flex items-center">
                    <Users className="w-5 h-5 text-blue-500 mr-1" />
                    <span className="font-semibold">{selectedStory.socialProof?.testimonials}</span>
                    <span className="text-gray-600 ml-1">testimonials</span>
                  </div>
                  <div className="flex items-center">
                    <TrendingUp className="w-5 h-5 text-green-500 mr-1" />
                    <span className="font-semibold">{selectedStory.socialProof?.projectsCompleted}</span>
                    <span className="text-gray-600 ml-1">projects</span>
                  </div>
                </div>

                {/* Full Story Content */}
                <div
                  className="prose prose-lg max-w-none"
                  dangerouslySetInnerHTML={{ __html: selectedStory.fullStory }}
                />

                {/* Tags */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Related Topics:</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedStory.tags?.map((tag, index) => (
                      <span key={index} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Chatbot */}
        <Chatbot
          currentPage="success"
          availableContent={selectedStory ? [selectedStory] : []}
          allContent={stories}
        />
      </div>
    </div>
  );
};

// Submit Story Modal Component
const SubmitStoryModal = ({ isOpen, onClose }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    hustle: '',
    story: '',
    earnings: '',
    timeframe: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await successStoriesAPI.submit(formData);
      alert('Thank you for sharing your success story! It will be reviewed and published soon.');
      onClose();
      setFormData({ name: '', hustle: '', story: '', earnings: '', timeframe: '' });
    } catch (error) {
      console.error('Error submitting story:', error);
      alert('Error submitting story. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Share Your Success Story">
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          label="Your Name"
          value={formData.name}
          onChange={(e) => setFormData({...formData, name: e.target.value})}
          required
        />
        <Input
          label="Your Hustle"
          value={formData.hustle}
          onChange={(e) => setFormData({...formData, hustle: e.target.value})}
          placeholder="e.g., Freelance Writing"
          required
        />
        <Textarea
          label="Your Story"
          value={formData.story}
          onChange={(e) => setFormData({...formData, story: e.target.value})}
          placeholder="Tell us about your journey..."
          rows={4}
          required
        />
        <Input
          label="Monthly Earnings"
          value={formData.earnings}
          onChange={(e) => setFormData({...formData, earnings: e.target.value})}
          placeholder="e.g., $3,000/month"
          required
        />
        <Input
          label="Time to Success"
          value={formData.timeframe}
          onChange={(e) => setFormData({...formData, timeframe: e.target.value})}
          placeholder="e.g., 6 months"
          required
        />

        <Modal.Footer>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" loading={isSubmitting}>
            Submit Story
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default SuccessStoriesPage;
