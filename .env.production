# Production Environment Variables for HustleGPT
# These will be used during build on Netlify

# Application Settings
VITE_APP_NAME=HustleGPT
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production
VITE_APP_URL=https://hustlegpt.netlify.app

# Feature Flags
VITE_USE_REAL_BACKEND=false
VITE_USE_FIREBASE_AUTH=false
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PREDICTIONS=true
VITE_ENABLE_REALTIME=false

# AI Service Configuration
VITE_AI_ENABLED=true
VITE_AI_ENDPOINT=https://openrouter.ai/api/v1/chat/completions
# Note: Set VITE_AI_API_KEY in Netlify dashboard for security
# VITE_AI_API_KEY=your-openrouter-api-key-here
VITE_AI_MODEL=meta-llama/llama-3.1-8b-instruct:free
VITE_AI_MAX_TOKENS=2000
VITE_AI_TEMPERATURE=0.7
VITE_AI_TIMEOUT=60000

# Performance Settings
VITE_CACHE_TTL=300000
VITE_CACHE_MAX_SIZE=100
VITE_CACHE_PERSISTENCE=true

# Analytics (set in Netlify dashboard)
VITE_ANALYTICS_ENABLED=true
# VITE_ANALYTICS_TRACKING_ID=your-ga-tracking-id

# Security Settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=error
