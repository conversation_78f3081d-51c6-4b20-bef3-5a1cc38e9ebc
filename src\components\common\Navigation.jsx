import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ROUTES, NAV_ITEMS } from '../../constants/routes';
import { useAuth } from '../../context/AuthContext';
import { User, LogOut, LogIn } from 'lucide-react';

const Navigation = () => {
  const location = useLocation();
  const { isAuthenticated, user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <nav className="bg-white shadow-lg border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <Link to={ROUTES.HOME} className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <span className="text-white font-bold text-lg">H</span>
              </div>
              <span className="text-xl font-bold text-gray-900">HustleGPT</span>
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            {NAV_ITEMS.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  location.pathname === item.path
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </div>

          {/* Auth Section - ONLY USER PROFILE (all pages are protected) */}
          <div className="flex items-center space-x-4">
            <Link
              to={ROUTES.DASHBOARD}
              className="flex items-center text-gray-700 hover:text-blue-600 transition-colors"
            >
              <User className="w-5 h-5 mr-1" />
              <span className="hidden sm:inline">{user?.name || 'Dashboard'}</span>
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center text-gray-700 hover:text-red-600 transition-colors"
            >
              <LogOut className="w-5 h-5 mr-1" />
              <span className="hidden sm:inline">Logout</span>
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
