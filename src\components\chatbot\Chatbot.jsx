import { useState, useEffect, useRef } from 'react';
import { MessageCircle, X, Send, Bot, ExternalLink } from 'lucide-react';
import { Button } from '../ui';
import { ChatbotService } from '../../services/chatbotService';

const Chatbot = ({ currentPage, availableContent = [], allContent = [] }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  // Scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage = {
        id: 'welcome',
        type: 'bot',
        content: getWelcomeMessage(currentPage),
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [isOpen, currentPage, messages.length]);

  const getWelcomeMessage = (page) => {
    const messages = {
      blog: "Hi! 👋 I can summarize this blog post in 7 key points. Just ask me to 'summarize this article' and I'll break it down for you!",
      trending: "Hello! 👋 I can analyze this trending hustle and give you 7 actionable insights. Ask me to 'summarize this hustle' to get started!",
      success: "Hey there! 👋 I can extract 7 key takeaways from this success story. Just ask me to 'summarize this story' and I'll help you learn from it!"
    };
    return messages[page] || "Hi! I'm here to help you understand content better with 7-point summaries!";
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    setInputValue('');
    setIsTyping(true);

    // Simulate processing delay
    setTimeout(() => {
      const response = processUserMessage(currentInput, currentPage, availableContent);
      
      const botMessage = {
        id: Date.now() + 1,
        type: 'bot',
        content: response.content,
        isHTML: response.isHTML,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const processUserMessage = (input, page, content) => {
    const processedInput = ChatbotService.processUserInput(input, content, allContent);

    // Handle summarization requests
    if (processedInput.intent === 'summarize' && processedInput.content) {
      let summaryData = null;

      // Generate summary based on content type
      if (page === 'blog') {
        summaryData = ChatbotService.summarizeBlogPost(processedInput.content);
      } else if (page === 'trending') {
        summaryData = ChatbotService.summarizeTrendingHustle(processedInput.content);
      } else if (page === 'success') {
        summaryData = ChatbotService.summarizeSuccessStory(processedInput.content);
      }

      if (summaryData) {
        const formattedResponse = ChatbotService.formatSummaryResponse(summaryData);
        return {
          content: generateSummaryHTML(formattedResponse),
          isHTML: true
        };
      }
    }

    // Handle search requests
    if (processedInput.intent === 'search' && processedInput.results) {
      const searchResponse = ChatbotService.formatSearchResults(processedInput.results, page);
      return {
        content: searchResponse.content,
        isHTML: searchResponse.isHTML
      };
    }

    // Default response
    return {
      content: ChatbotService.getDefaultResponse(page, input),
      isHTML: false
    };
  };

  const generateSummaryHTML = (responseData) => {
    const metadata = responseData.metadata || {};
    
    return `
      <div class="summary-response">
        <div class="mb-4">
          <h3 class="font-bold text-lg text-blue-600 mb-2">
            ${responseData.title}
          </h3>
          <p class="text-sm text-gray-600 mb-3">${responseData.subtitle}</p>
        </div>
        
        <div class="space-y-3 mb-4">
          ${responseData.points.map(point => `
            <div class="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
              <div class="w-7 h-7 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                ${point.point}
              </div>
              <div class="flex-1">
                <h4 class="font-semibold text-gray-900 text-sm mb-1">${point.title}</h4>
                <p class="text-gray-700 text-sm leading-relaxed">${point.content}</p>
              </div>
            </div>
          `).join('')}
        </div>
        
        ${Object.keys(metadata).length > 0 ? `
          <div class="mb-4 p-3 bg-blue-50 rounded-lg">
            <div class="flex flex-wrap gap-4 text-xs text-gray-600">
              ${metadata.readTime ? `<span>📖 ${metadata.readTime}</span>` : ''}
              ${metadata.category ? `<span>🏷️ ${metadata.category}</span>` : ''}
              ${metadata.earnings ? `<span>💰 ${metadata.earnings}</span>` : ''}
              ${metadata.timeframe ? `<span>⏱️ ${metadata.timeframe}</span>` : ''}
              ${metadata.difficulty ? `<span>📊 ${metadata.difficulty}</span>` : ''}
            </div>
          </div>
        ` : ''}
        
        <div class="flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-200">
          <svg class="w-4 h-4 text-green-600 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5z"/>
            <path d="M7.414 15.414a2 2 0 01-2.828-2.828l3-3a2 2 0 012.828 0 1 1 0 001.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5z"/>
          </svg>
          <span class="text-green-700 font-medium text-sm flex-1">
            Read the full content for complete details
          </span>
          <button onclick="window.location.href='${responseData.link}'" class="text-green-600 hover:text-green-800 font-medium text-sm flex items-center gap-1">
            View Full Content
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"/>
              <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"/>
            </svg>
          </button>
        </div>
      </div>
    `;
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Chatbot Toggle Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setIsOpen(!isOpen)}
          className="w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 bg-blue-600 hover:bg-blue-700"
          size="lg"
        >
          {isOpen ? <X className="w-6 h-6" /> : <MessageCircle className="w-6 h-6" />}
        </Button>
      </div>

      {/* Chatbot Window */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 w-96 h-[500px] bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col z-50 max-w-[calc(100vw-3rem)] max-h-[calc(100vh-8rem)]">
          {/* Header */}
          <div className="p-4 border-b border-gray-200 bg-blue-600 text-white rounded-t-lg">
            <div className="flex items-center gap-3">
              <Bot className="w-6 h-6" />
              <div>
                <h3 className="font-semibold">Content Summarizer</h3>
                <p className="text-xs opacity-90">Get 7-point summaries instantly</p>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map(message => (
              <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[85%] p-3 rounded-lg ${
                  message.type === 'user' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {message.isHTML ? (
                    <div 
                      dangerouslySetInnerHTML={{ __html: message.content }}
                      className="prose prose-sm max-w-none"
                    />
                  ) : (
                    <p className="text-sm leading-relaxed">{message.content}</p>
                  )}
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 p-3 rounded-lg">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex gap-2">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me to summarize this content..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                disabled={isTyping}
              />
              <Button 
                onClick={handleSendMessage} 
                size="sm"
                disabled={isTyping || !inputValue.trim()}
                className="px-3"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Chatbot;
