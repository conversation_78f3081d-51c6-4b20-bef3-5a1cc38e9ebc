<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Gradient -->
  <rect width="1200" height="630" fill="url(#bgGradient)" />
  
  <!-- Background Pattern -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="1200" height="630" fill="url(#grid)" />
  
  <!-- Main Logo -->
  <g transform="translate(100, 200)">
    <!-- Logo Circle -->
    <circle cx="60" cy="60" r="50" fill="url(#logoGradient)" />
    
    <!-- AI Brain/Circuit Pattern -->
    <g stroke="#ffffff" stroke-width="2" fill="none" opacity="0.9">
      <!-- Central Hub -->
      <circle cx="60" cy="60" r="6" fill="#ffffff" />
      
      <!-- Neural Network Lines -->
      <path d="M60 54 L52 42 M60 54 L68 42 M60 66 L52 78 M60 66 L68 78" stroke-linecap="round" />
      <path d="M54 60 L42 52 M54 60 L42 68 M66 60 L78 52 M66 60 L78 68" stroke-linecap="round" />
      
      <!-- Connection Nodes -->
      <circle cx="52" cy="42" r="2.5" fill="#ffffff" />
      <circle cx="68" cy="42" r="2.5" fill="#ffffff" />
      <circle cx="52" cy="78" r="2.5" fill="#ffffff" />
      <circle cx="68" cy="78" r="2.5" fill="#ffffff" />
      <circle cx="42" cy="52" r="2.5" fill="#ffffff" />
      <circle cx="42" cy="68" r="2.5" fill="#ffffff" />
      <circle cx="78" cy="52" r="2.5" fill="#ffffff" />
      <circle cx="78" cy="68" r="2.5" fill="#ffffff" />
    </g>
    
    <!-- Dollar Sign Overlay -->
    <g fill="#ffffff" opacity="0.8">
      <path d="M58 48 L58 44 L62 44 L62 48 M58 72 L58 76 L62 76 L62 72" stroke="#ffffff" stroke-width="2" />
      <path d="M54 52 C54 50 56 48 60 48 C64 48 66 50 66 52 C66 54 64 54 60 56 C56 58 54 58 54 60 C54 62 56 64 60 64 C64 64 66 62 66 60" 
            stroke="#ffffff" stroke-width="2.5" fill="none" stroke-linecap="round" />
    </g>
  </g>
  
  <!-- Main Title -->
  <text x="250" y="280" font-family="Arial, sans-serif" font-size="72" font-weight="bold" fill="#ffffff">
    HustleGPT
  </text>
  
  <!-- Subtitle -->
  <text x="250" y="340" font-family="Arial, sans-serif" font-size="36" fill="rgba(255,255,255,0.9)">
    AI-Powered Side Hustle Discovery Platform
  </text>
  
  <!-- Feature Points -->
  <g fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="24">
    <text x="250" y="400">✨ AI-Powered Recommendations</text>
    <text x="250" y="440">🎯 Success Prediction Algorithm</text>
    <text x="250" y="480">💬 Interactive AI Chatbot</text>
    <text x="250" y="520">📈 Personalized Growth Tracking</text>
  </g>
  
  <!-- Call to Action -->
  <rect x="250" y="550" width="200" height="50" rx="25" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.5)" stroke-width="2" />
  <text x="350" y="580" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#ffffff" text-anchor="middle">
    Get Started Today
  </text>
  
  <!-- Decorative Elements -->
  <g opacity="0.3">
    <!-- Floating Icons -->
    <circle cx="900" cy="150" r="30" fill="rgba(255,255,255,0.1)" />
    <circle cx="1000" cy="200" r="20" fill="rgba(255,255,255,0.1)" />
    <circle cx="950" cy="300" r="25" fill="rgba(255,255,255,0.1)" />
    <circle cx="1050" cy="350" r="15" fill="rgba(255,255,255,0.1)" />
    <circle cx="900" cy="450" r="35" fill="rgba(255,255,255,0.1)" />
    <circle cx="1020" cy="500" r="18" fill="rgba(255,255,255,0.1)" />
  </g>
  
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
