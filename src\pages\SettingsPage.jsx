import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { <PERSON><PERSON>, <PERSON> } from '../components/ui';
import { 
  Bell, 
  Shield, 
  Moon, 
  Globe, 
  Mail, 
  Smartphone, 
  Eye, 
  Lock,
  Trash2,
  Download,
  Upload
} from 'lucide-react';

const SettingsPage = () => {
  const { user } = useAuth();
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: true,
      marketing: false,
      updates: true
    },
    privacy: {
      profileVisible: true,
      showEmail: false,
      showActivity: true
    },
    preferences: {
      darkMode: false,
      language: 'en',
      timezone: 'UTC'
    }
  });

  const handleToggle = (category, setting) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: !prev[category][setting]
      }
    }));
  };

  const handleSelectChange = (category, setting, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value
      }
    }));
  };

  const ToggleSwitch = ({ enabled, onToggle }) => (
    <button
      onClick={onToggle}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
        enabled ? 'bg-blue-600' : 'bg-gray-200'
      }`}
    >
      <span
        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
          enabled ? 'translate-x-6' : 'translate-x-1'
        }`}
      />
    </button>
  );

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-2">Manage your account preferences and privacy settings</p>
        </div>

        <div className="space-y-6">
          {/* Notifications */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center">
                <Bell className="w-5 h-5 mr-2" />
                Notifications
              </Card.Title>
            </Card.Header>
            <Card.Content className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Email Notifications</h4>
                  <p className="text-sm text-gray-600">Receive notifications via email</p>
                </div>
                <ToggleSwitch
                  enabled={settings.notifications.email}
                  onToggle={() => handleToggle('notifications', 'email')}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Push Notifications</h4>
                  <p className="text-sm text-gray-600">Receive push notifications in browser</p>
                </div>
                <ToggleSwitch
                  enabled={settings.notifications.push}
                  onToggle={() => handleToggle('notifications', 'push')}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Marketing Emails</h4>
                  <p className="text-sm text-gray-600">Receive promotional content and updates</p>
                </div>
                <ToggleSwitch
                  enabled={settings.notifications.marketing}
                  onToggle={() => handleToggle('notifications', 'marketing')}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Product Updates</h4>
                  <p className="text-sm text-gray-600">Get notified about new features</p>
                </div>
                <ToggleSwitch
                  enabled={settings.notifications.updates}
                  onToggle={() => handleToggle('notifications', 'updates')}
                />
              </div>
            </Card.Content>
          </Card>

          {/* Privacy */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                Privacy & Security
              </Card.Title>
            </Card.Header>
            <Card.Content className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Public Profile</h4>
                  <p className="text-sm text-gray-600">Make your profile visible to other users</p>
                </div>
                <ToggleSwitch
                  enabled={settings.privacy.profileVisible}
                  onToggle={() => handleToggle('privacy', 'profileVisible')}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Show Email</h4>
                  <p className="text-sm text-gray-600">Display email address on profile</p>
                </div>
                <ToggleSwitch
                  enabled={settings.privacy.showEmail}
                  onToggle={() => handleToggle('privacy', 'showEmail')}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Activity Status</h4>
                  <p className="text-sm text-gray-600">Show your activity and progress</p>
                </div>
                <ToggleSwitch
                  enabled={settings.privacy.showActivity}
                  onToggle={() => handleToggle('privacy', 'showActivity')}
                />
              </div>

              <div className="pt-4 border-t border-gray-200">
                <Button variant="outline" leftIcon={<Lock className="w-4 h-4" />}>
                  Change Password
                </Button>
              </div>
            </Card.Content>
          </Card>

          {/* Preferences */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center">
                <Globe className="w-5 h-5 mr-2" />
                Preferences
              </Card.Title>
            </Card.Header>
            <Card.Content className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Dark Mode</h4>
                  <p className="text-sm text-gray-600">Use dark theme across the application</p>
                </div>
                <ToggleSwitch
                  enabled={settings.preferences.darkMode}
                  onToggle={() => handleToggle('preferences', 'darkMode')}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Language
                </label>
                <select
                  value={settings.preferences.language}
                  onChange={(e) => handleSelectChange('preferences', 'language', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Timezone
                </label>
                <select
                  value={settings.preferences.timezone}
                  onChange={(e) => handleSelectChange('preferences', 'timezone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="UTC">UTC</option>
                  <option value="EST">Eastern Time</option>
                  <option value="PST">Pacific Time</option>
                  <option value="GMT">Greenwich Mean Time</option>
                </select>
              </div>
            </Card.Content>
          </Card>

          {/* Data Management */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center">
                <Download className="w-5 h-5 mr-2" />
                Data Management
              </Card.Title>
            </Card.Header>
            <Card.Content className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Export Data</h4>
                  <p className="text-sm text-gray-600">Download a copy of your data</p>
                </div>
                <Button variant="outline" size="sm" leftIcon={<Download className="w-4 h-4" />}>
                  Export
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Import Data</h4>
                  <p className="text-sm text-gray-600">Import data from another service</p>
                </div>
                <Button variant="outline" size="sm" leftIcon={<Upload className="w-4 h-4" />}>
                  Import
                </Button>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-red-600">Delete Account</h4>
                    <p className="text-sm text-gray-600">Permanently delete your account and data</p>
                  </div>
                  <Button variant="outline" size="sm" leftIcon={<Trash2 className="w-4 h-4" />} className="text-red-600 border-red-300 hover:bg-red-50">
                    Delete
                  </Button>
                </div>
              </div>
            </Card.Content>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button size="lg">
              Save All Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
