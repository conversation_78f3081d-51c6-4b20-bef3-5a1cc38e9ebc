// API service functions for HustleGPT
import {
  extendedMockHustles as mockHustles,
  extendedMockBlogPosts as mockBlogPosts,
  extendedMockSuccessStories as mockSuccessStories,
  demoCredentials
} from '../data/mockData';

// Base API configuration
const API_BASE_URL = 'http://localhost:3001/api';

// Generic API request function
const apiRequest = async (endpoint, options = {}) => {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API Request failed:', error);
    throw error;
  }
};

// Auth API functions
export const authAPI = {
  login: async (email, password) => {
    // TODO: Replace with actual API call
    // For now, simulate API call with mock data
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (email === demoCredentials.email && password === demoCredentials.password) {
      return {
        success: true,
        user: demoCredentials.user,
        token: 'mock-jwt-token'
      };
    }

    // Allow any email/password for demo purposes
    if (email && password && password.length >= 6) {
      return {
        success: true,
        user: {
          id: Date.now().toString(),
          name: email.split('@')[0].replace(/[^a-zA-Z]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          email: email,
          avatar: null,
          joinedDate: new Date().toISOString(),
          hustlesTried: 0,
          successStories: 0,
          monthlyEarnings: 0
        },
        token: 'mock-jwt-token'
      };
    }

    throw new Error('Invalid credentials');
  },

  signup: async (name, email, password) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      success: true,
      user: {
        id: Date.now().toString(),
        name,
        email,
        avatar: null,
      },
      token: 'mock-jwt-token'
    };
  },

  logout: async () => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  }
};

// Hustles API functions
export const hustlesAPI = {
  getAll: async () => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockHustles;
  },

  getById: async (id) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const hustle = mockHustles.find(h => h.id === id);
    if (!hustle) throw new Error('Hustle not found');
    return hustle;
  },

  getTrending: async () => {
    await new Promise(resolve => setTimeout(resolve, 400));
    return mockHustles.filter(h => h.trending).sort((a, b) => b.popularity - a.popularity);
  },

  search: async (query) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockHustles.filter(h => 
      h.title.toLowerCase().includes(query.toLowerCase()) ||
      h.description.toLowerCase().includes(query.toLowerCase()) ||
      h.category.toLowerCase().includes(query.toLowerCase())
    );
  }
};

// Blog API functions
export const blogAPI = {
  getAll: async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockBlogPosts;
  },

  getById: async (id) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const post = mockBlogPosts.find(p => p.id === id);
    if (!post) throw new Error('Blog post not found');
    return post;
  },

  getFeatured: async () => {
    await new Promise(resolve => setTimeout(resolve, 400));
    return mockBlogPosts.filter(p => p.featured);
  }
};

// Success Stories API functions
export const successStoriesAPI = {
  getAll: async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockSuccessStories;
  },

  getFeatured: async () => {
    await new Promise(resolve => setTimeout(resolve, 400));
    return mockSuccessStories.filter(s => s.featured);
  },

  submit: async (storyData) => {
    await new Promise(resolve => setTimeout(resolve, 800));
    return {
      success: true,
      message: 'Success story submitted successfully!'
    };
  }
};

// AI Finder API functions
export const aiFinderAPI = {
  // AI-powered recommendations - AI ONLY MODE
  getAIRecommendations: async (userProfile) => {
    console.log('🤖 Calling AI API with user profile (AI-ONLY MODE)...');

    const aiResult = await callExternalAI(userProfile);

    if (!aiResult || !Array.isArray(aiResult) || aiResult.length === 0) {
      throw new Error('AI returned no valid recommendations');
    }

    return aiResult;
  },

  // EXISTING: Basic recommendations (fallback)
  getRecommendations: async (interests, skills = [], experience = 'beginner') => {
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simple recommendation logic based on interests
    const recommendations = mockHustles.filter(hustle => {
      const interestMatch = interests.some(interest =>
        hustle.title.toLowerCase().includes(interest.toLowerCase()) ||
        hustle.description.toLowerCase().includes(interest.toLowerCase()) ||
        hustle.category.toLowerCase().includes(interest.toLowerCase())
      );

      const difficultyMatch = experience === 'beginner' ?
        hustle.difficulty === 'Beginner' || hustle.difficulty === 'Intermediate' :
        true;

      return interestMatch || difficultyMatch;
    });

    return recommendations.length > 0 ? recommendations : mockHustles.slice(0, 3);
  }
};

// Contact API functions
export const contactAPI = {
  submit: async (contactData) => {
    await new Promise(resolve => setTimeout(resolve, 800));
    return {
      success: true,
      message: 'Message sent successfully! We\'ll get back to you soon.'
    };
  }
};

// Import AI configuration
import { AI_CONFIG, validateAIConfig } from '../config/ai';

// External AI API Integration Function - AI ONLY
const callExternalAI = async (userProfile) => {
  // Validate AI configuration
  const configValidation = validateAIConfig();
  if (!configValidation.valid) {
    throw new Error(`AI Configuration Error: ${configValidation.error}`);
  }

  console.log(`🤖 Using AI model: ${AI_CONFIG.model}`);
  return await callAIWithModel(userProfile, AI_CONFIG.model);
};





// Call AI with specific model
const callAIWithModel = async (userProfile, model) => {

  // Prepare comprehensive prompt for Nvidia Llama to analyze ALL quiz answers
  const aiPrompt = `You are an expert side hustle advisor. Analyze this complete user profile from a 6-step questionnaire and provide highly personalized side hustle recommendations.

COMPLETE USER PROFILE ANALYSIS:

STEP 1 - BUDGET ANALYSIS:
Budget Choice: "${userProfile.budget}"
${userProfile.budget === 'no-investment' ? 'User wants zero upfront investment hustles' :
  userProfile.budget === 'low-budget' ? 'User can invest $100-500 initially' :
  userProfile.budget === 'medium-budget' ? 'User can invest $500-2000 initially' :
  'User has significant capital for high-investment opportunities'}

STEP 2 - INTERESTS ANALYSIS:
Selected Interests: ${userProfile.interests.join(', ')}
Interest Count: ${userProfile.interests.length} interests selected
Primary Focus: ${userProfile.interests[0]}

STEP 3 - SKILLS ANALYSIS:
Available Skills: ${userProfile.skills.join(', ')}
Skill Count: ${userProfile.skills.length} skills available
Core Strength: ${userProfile.skills[0]}

STEP 4 - EXPERIENCE ANALYSIS:
Experience Level: "${userProfile.experience}"
${userProfile.experience === 'complete-beginner' ? 'Needs beginner-friendly opportunities with learning support' :
  userProfile.experience === 'some-experience' ? 'Can handle intermediate challenges with guidance' :
  'Ready for advanced opportunities and complex projects'}

STEP 5 - TIME ANALYSIS:
Time Commitment: "${userProfile.timeCommitment}"
${userProfile.timeCommitment === 'part-time' ? 'Needs flexible, part-time opportunities (10-20 hours/week)' :
  userProfile.timeCommitment === 'full-time' ? 'Can dedicate full-time effort (40+ hours/week)' :
  'Prefers weekend or evening work'}

STEP 6 - GOALS & WORK STYLE ANALYSIS:
Primary Goals: ${userProfile.goals.join(', ')}
Work Style: "${userProfile.workStyle}"
${userProfile.workStyle === 'flexible' ? 'Prefers flexible, autonomous work arrangements' :
  userProfile.workStyle === 'structured' ? 'Thrives with clear structure and deadlines' :
  'Enjoys collaborative team environments'}

TASK: Based on this comprehensive analysis, provide exactly 6 highly personalized side hustle recommendations that perfectly match ALL aspects of this user's profile.

RETURN ONLY THIS JSON FORMAT (no other text):
{
  "recommendations": [
    {
      "title": "Specific hustle name that matches their interests and skills",
      "description": "Detailed 2-3 sentence description explaining exactly how this hustle works and why it's perfect for them",
      "category": "Main category from their interests",
      "difficulty": "Beginner/Intermediate/Advanced (match their experience)",
      "timeCommitment": "X-Y hours per week (match their availability)",
      "earnings": "$X-Y per month (realistic based on their budget/experience)",
      "confidence": 85,
      "reasons": [
        "Specific reason connecting to their budget choice",
        "Specific reason connecting to their interests/skills",
        "Specific reason connecting to their goals/work style"
      ],
      "requirements": ["Skill from their list", "Additional relevant skill"],
      "tags": ["Interest tag", "Skill tag", "Goal tag"]
    }
  ]
}

CRITICAL REQUIREMENTS:
- Each recommendation must connect to at least 3 different aspects of their profile
- Confidence scores should be 75-95% based on profile match strength
- Earnings must be realistic for their experience level and time commitment
- All recommendations must respect their budget constraints
- Focus on actionable, real-world opportunities they can start immediately`;

  try {
    // Create timeout controller
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), AI_CONFIG.timeout || 60000);

    // Make API call to OpenRouter with specific model
    const response = await fetch(AI_CONFIG.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AI_CONFIG.apiKey}`,
        'HTTP-Referer': AI_CONFIG.httpReferer || 'https://hustlegpt.netlify.app',
        'X-Title': AI_CONFIG.xTitle || 'HustleGPT AI Finder',
      },
      body: JSON.stringify({
        model: model, // Use the specific model passed to this function
        messages: [
          {
            role: 'system',
            content: 'You are an expert side hustle advisor. Analyze user profiles and recommend personalized side hustles with detailed explanations.'
          },
          {
            role: 'user',
            content: aiPrompt
          }
        ],
        max_tokens: AI_CONFIG.maxTokens,
        temperature: AI_CONFIG.temperature,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      }),
      signal: controller.signal,
    });

    // Clear timeout
    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('OpenRouter API error response:', errorText);
      throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
    }

    const aiResponse = await response.json();

    // Validate AI response structure
    if (!aiResponse || typeof aiResponse !== 'object') {
      throw new Error('Invalid AI response format');
    }

    // Process AI response and convert to our format
    const recommendations = processAIResponse(aiResponse, userProfile);

    // Validate processed recommendations
    if (!recommendations || !Array.isArray(recommendations) || recommendations.length === 0) {
      throw new Error('AI returned no valid recommendations');
    }

    return recommendations;
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('AI API call timed out');
      throw new Error('AI request timed out. Please try again.');
    }
    console.error('External AI API call failed:', error);
    throw error;
  }
};

// Process OpenRouter Llama API response and convert to our format
const processAIResponse = (aiResponse, userProfile) => {
  try {
    let recommendations = [];

    // OpenRouter returns response in choices[0].message.content
    if (aiResponse.choices && aiResponse.choices[0] && aiResponse.choices[0].message) {
      const content = aiResponse.choices[0].message.content.trim();

      try {
        // Parse the JSON response from Llama
        const parsedResponse = JSON.parse(content);

        // Extract recommendations array
        if (parsedResponse.recommendations && Array.isArray(parsedResponse.recommendations)) {
          recommendations = parsedResponse.recommendations;
        } else if (Array.isArray(parsedResponse)) {
          recommendations = parsedResponse;
        } else {
          throw new Error('Invalid response format');
        }
      } catch (parseError) {
        console.error('Error parsing Llama JSON response:', parseError);
        console.log('Raw content:', content);
        throw new Error('Failed to parse AI response as JSON');
      }
    } else {
      throw new Error('Invalid OpenRouter response structure');
    }

    // Validate recommendations array
    if (!Array.isArray(recommendations) || recommendations.length === 0) {
      throw new Error('No recommendations found in AI response');
    }

    // Convert to our internal format with validation
    const processedRecommendations = recommendations
      .filter(rec => rec && typeof rec === 'object') // Filter out invalid entries
      .map((rec, index) => ({
        id: `ai-${Date.now()}-${index}`,
        title: (rec.title || rec.name || 'AI Recommended Hustle').substring(0, 100), // Limit title length
        description: (rec.description || rec.summary || 'AI recommended side hustle').substring(0, 500), // Limit description
        category: rec.category || 'AI Recommended',
        difficulty: ['Beginner', 'Intermediate', 'Advanced'].includes(rec.difficulty) ? rec.difficulty : 'Intermediate',
        timeCommitment: rec.timeCommitment || rec.time || 'Flexible',
        potentialEarnings: rec.earnings || rec.income || '$500-2000/month',
        requirements: Array.isArray(rec.requirements) ? rec.requirements : (Array.isArray(rec.skills) ? rec.skills : []),
        trending: Boolean(rec.trending),
        popularity: Math.min(Math.max(Number(rec.confidence || rec.score || 85), 0), 100), // Ensure 0-100 range
        tags: Array.isArray(rec.tags) ? rec.tags : (Array.isArray(rec.keywords) ? rec.keywords : []),
        // AI-specific fields
        aiGenerated: true,
        aiScore: Math.min(Math.max(Number(rec.confidence || rec.score || 85), 0), 100),
        matchReasons: Array.isArray(rec.reasons) ? rec.reasons.slice(0, 5) : (Array.isArray(rec.whyMatch) ? rec.whyMatch.slice(0, 5) : []),
        confidence: Math.min(Math.max(Number(rec.confidence || rec.score || 85), 0), 100),
        analysisDate: new Date().toISOString(),
        userProfile: {
          budget: userProfile.budget,
          interests: userProfile.interests.length,
          skills: userProfile.skills.length,
          experience: userProfile.experience,
          goals: userProfile.goals.length
        }
      }))
      .filter(rec => rec.title && rec.description); // Filter out entries without title/description

    // Ensure we have at least some recommendations
    if (processedRecommendations.length === 0) {
      throw new Error('No valid recommendations after processing');
    }

    return processedRecommendations;
  } catch (error) {
    console.error('Error processing AI response:', error);
    throw new Error('Failed to process AI recommendations');
  }
};
