import { useState, useEffect, useCallback } from 'react';
import { TrendingUp, TrendingDown, Flame, Clock, DollarSign, Users, Eye, Heart, ArrowRight, Filter, Play, Pause, Square, Volume2, VolumeX } from 'lucide-react';
import { <PERSON><PERSON>, Card, Badge } from '../components/ui';
import { LoadingSpinner } from '../components/common';
import { hustlesAPI } from '../services/api';
import Chatbot from '../components/chatbot/Chatbot';

const TrendingPage = () => {
  const [allHustles, setAllHustles] = useState([]);
  const [trendingHustles, setTrendingHustles] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [timeFilter, setTimeFilter] = useState('weekly');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedHustle, setSelectedHustle] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // Text-to-Speech state
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentUtterance, setCurrentUtterance] = useState(null);
  const [speechProgress, setSpeechProgress] = useState(0);
  const [speechRate, setSpeechRate] = useState(1);
  const [speechVolume, setSpeechVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [currentSentence, setCurrentSentence] = useState('');

  const timeFilters = [
    { value: 'daily', label: 'Today' },
    { value: 'weekly', label: 'This Week' },
    { value: 'monthly', label: 'This Month' }
  ];

  const categories = ['All', 'Writing', 'Technology', 'Marketing', 'Education', 'E-commerce', 'Design'];

  // Apply filters to the hustles
  const applyFilters = useCallback((hustles) => {
    let filtered = [...hustles];

    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(hustle =>
        hustle.category.toLowerCase() === categoryFilter.toLowerCase()
      );
    }

    // Apply time filter (simulate different trending data for different time periods)
    if (timeFilter === 'daily') {
      // Show more recent/hot trends for daily
      filtered = filtered.filter(hustle => hustle.velocity === 'hot' || hustle.trend === 'up');
    } else if (timeFilter === 'monthly') {
      // Show more established trends for monthly
      filtered = filtered.sort((a, b) => b.views - a.views);
    }

    // Re-rank after filtering
    const reranked = filtered.map((hustle, index) => ({
      ...hustle,
      rank: index + 1
    }));

    setTrendingHustles(reranked);
  }, [categoryFilter, timeFilter]);

  const loadTrendingHustles = useCallback(async () => {
    setIsLoading(true);
    try {
      const trending = await hustlesAPI.getTrending();

      // Add comprehensive trending data with detailed information
      const trendingWithStats = trending.map((hustle, index) => ({
        ...hustle,
        rank: index + 1,
        views: Math.floor(Math.random() * 10000) + 1000,
        saves: Math.floor(Math.random() * 1000) + 100,
        trend: Math.random() > 0.3 ? 'up' : 'down',
        trendPercentage: Math.floor(Math.random() * 50) + 5,
        velocity: Math.random() > 0.5 ? 'hot' : 'rising',
        // Add detailed information for Learn More functionality
        detailedDescription: `${hustle.description} This comprehensive guide covers everything you need to know about ${hustle.title.toLowerCase()}, including step-by-step instructions, required tools, potential challenges, and success strategies from experienced practitioners.`,
        requirements: hustle.requirements || ['Basic computer skills', 'Internet connection', 'Time commitment'],
        pros: [
          'Flexible working hours',
          'High earning potential',
          'Low startup costs',
          'Scalable business model'
        ],
        cons: [
          'Initial learning curve',
          'Market competition',
          'Income variability',
          'Self-discipline required'
        ],
        successTips: [
          'Start with small projects to build experience',
          'Focus on building a strong portfolio',
          'Network with other professionals in the field',
          'Continuously update your skills and knowledge'
        ],
        averageTimeToProfit: '2-4 months',
        skillLevel: hustle.difficulty || 'Intermediate',
        marketDemand: Math.random() > 0.5 ? 'High' : 'Medium',
        competition: Math.random() > 0.5 ? 'Medium' : 'High'
      }));

      setAllHustles(trendingWithStats);
      applyFilters(trendingWithStats);
    } catch (error) {
      console.error('Error loading trending hustles:', error);
    } finally {
      setIsLoading(false);
    }
  }, [applyFilters]);

  useEffect(() => {
    loadTrendingHustles();
  }, [loadTrendingHustles]); // Load initial data

  useEffect(() => {
    if (allHustles.length > 0) {
      applyFilters(allHustles);
    }
  }, [timeFilter, categoryFilter, allHustles, applyFilters]);

  // Cleanup TTS on unmount
  useEffect(() => {
    return () => {
      if (currentUtterance) {
        speechSynthesis.cancel();
      }
    };
  }, [currentUtterance]);

  const handleLearnMore = (hustle) => {
    setSelectedHustle(hustle);
    setShowDetailModal(true);
  };

  const handleCloseDetail = () => {
    setShowDetailModal(false);
    setSelectedHustle(null);
    // Stop any playing speech when closing modal
    if (currentUtterance) {
      speechSynthesis.cancel();
      setIsPlaying(false);
      setIsPaused(false);
      setSpeechProgress(0);
      setCurrentUtterance(null);
      setCurrentSentence('');
    }
  };

  // Text-to-Speech Functions
  const getHustleTextContent = (hustle) => {
    if (!hustle) return '';

    // Extract text from HTML content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = hustle.detailedDescription || '';
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Combine title, description, and detailed content
    return `${hustle.title}. ${hustle.description}. ${textContent}. Requirements: ${hustle.requirements?.join(', ')}. Success Tips: ${hustle.successTips?.join('. ')}`;
  };

  const startSpeech = () => {
    if (!selectedHustle) return;

    // Stop any existing speech
    if (currentUtterance) {
      speechSynthesis.cancel();
    }

    const textToSpeak = getHustleTextContent(selectedHustle);
    const utterance = new SpeechSynthesisUtterance(textToSpeak);

    // Configure speech settings
    utterance.rate = speechRate;
    utterance.volume = isMuted ? 0 : speechVolume;
    utterance.pitch = 1;

    // Event handlers
    utterance.onstart = () => {
      setIsPlaying(true);
      setIsPaused(false);
      setSpeechProgress(0);
    };

    utterance.onend = () => {
      setIsPlaying(false);
      setIsPaused(false);
      setSpeechProgress(100);
      setCurrentUtterance(null);
      setCurrentSentence('');
    };

    utterance.onerror = () => {
      setIsPlaying(false);
      setIsPaused(false);
      setCurrentUtterance(null);
      setCurrentSentence('');
    };

    utterance.onboundary = (event) => {
      if (event.name === 'sentence') {
        const progress = (event.charIndex / textToSpeak.length) * 100;
        setSpeechProgress(progress);

        // Extract current sentence for display
        const sentences = textToSpeak.split(/[.!?]+/);
        let charCount = 0;
        for (let sentence of sentences) {
          charCount += sentence.length + 1;
          if (charCount > event.charIndex) {
            setCurrentSentence(sentence.trim());
            break;
          }
        }
      }
    };

    setCurrentUtterance(utterance);
    speechSynthesis.speak(utterance);
  };

  const pauseSpeech = () => {
    if (speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
      setIsPaused(true);
    }
  };

  const resumeSpeech = () => {
    if (speechSynthesis.paused) {
      speechSynthesis.resume();
      setIsPaused(false);
    }
  };

  const stopSpeech = () => {
    speechSynthesis.cancel();
    setIsPlaying(false);
    setIsPaused(false);
    setSpeechProgress(0);
    setCurrentUtterance(null);
    setCurrentSentence('');
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    if (currentUtterance) {
      currentUtterance.volume = isMuted ? speechVolume : 0;
    }
  };

  const handleRateChange = (newRate) => {
    setSpeechRate(newRate);
    if (isPlaying && currentUtterance) {
      // Restart with new rate
      const wasPlaying = isPlaying;
      stopSpeech();
      if (wasPlaying) {
        setTimeout(startSpeech, 100);
      }
    }
  };

  const getTrendIcon = (trend) => {
    return trend === 'up' ?
      <TrendingUp className="w-4 h-4 text-green-500" /> :
      <TrendingDown className="w-4 h-4 text-red-500" />;
  };

  const getVelocityBadge = (velocity) => {
    if (velocity === 'hot') {
      return <Badge variant="danger" className="flex items-center"><Flame className="w-3 h-3 mr-1" />Hot</Badge>;
    }
    return <Badge variant="warning" className="flex items-center"><TrendingUp className="w-3 h-3 mr-1" />Rising</Badge>;
  };

  if (isLoading) {
    return <LoadingSpinner fullScreen text="Loading trending hustles..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Trending Hustles
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover what's hot right now. See the most popular side hustles based on community activity and engagement.
          </p>
        </div>

        {/* Trending Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="text-center p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
              <Eye className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">125K</div>
            <div className="text-gray-600">Views Today</div>
          </Card>
          <Card className="text-center p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-4">
              <Heart className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">8.5K</div>
            <div className="text-gray-600">Saves Today</div>
          </Card>
          <Card className="text-center p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-4">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">2.3K</div>
            <div className="text-gray-600">New Users</div>
          </Card>
          <Card className="text-center p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mb-4">
              <Flame className="w-6 h-6 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">15</div>
            <div className="text-gray-600">Hot Hustles</div>
          </Card>
        </div>

        {/* Filters */}
        <Card className="p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="flex items-center gap-4 flex-1">
              <Filter className="w-5 h-5 text-gray-600" />
              <div className="flex flex-col sm:flex-row gap-6 flex-1">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Time Period</label>
                  <div className="flex gap-2">
                    {timeFilters.map((filter) => (
                      <Button
                        key={filter.value}
                        variant={timeFilter === filter.value ? 'primary' : 'outline'}
                        size="sm"
                        onClick={() => setTimeFilter(filter.value)}
                        className={`transition-all duration-200 ${
                          timeFilter === filter.value ? 'shadow-md' : 'hover:shadow-sm'
                        }`}
                      >
                        {filter.label}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {categories.map(cat => (
                      <option key={cat} value={cat.toLowerCase()}>{cat}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Results Count */}
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center">
                <TrendingUp className="w-4 h-4 mr-1" />
                <span className="font-medium">{trendingHustles.length}</span>
                <span className="ml-1">trending hustles</span>
              </div>
              {(timeFilter !== 'weekly' || categoryFilter !== 'all') && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setTimeFilter('weekly');
                    setCategoryFilter('all');
                  }}
                  className="text-xs"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          </div>
        </Card>

        {/* Trending List */}
        <div className="space-y-4">
          {trendingHustles.map((hustle, index) => (
            <Card key={hustle.id} className="hover:shadow-lg transition-shadow">
              <Card.Content className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1">
                    {/* Rank */}
                    <div className="flex-shrink-0">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold ${
                        index === 0 ? 'bg-yellow-100 text-yellow-800' :
                        index === 1 ? 'bg-gray-100 text-gray-800' :
                        index === 2 ? 'bg-orange-100 text-orange-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        #{hustle.rank}
                      </div>
                    </div>

                    {/* Hustle Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{hustle.title}</h3>
                        {getVelocityBadge(hustle.velocity)}
                        <Badge variant="outline">{hustle.category}</Badge>
                      </div>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{hustle.description}</p>
                      <div className="flex items-center space-x-6 text-sm text-gray-500">
                        <div className="flex items-center">
                          <DollarSign className="w-4 h-4 mr-1" />
                          {hustle.potentialEarnings}
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {hustle.timeCommitment}
                        </div>
                        <div className="flex items-center">
                          <Eye className="w-4 h-4 mr-1" />
                          {hustle.views.toLocaleString()} views
                        </div>
                        <div className="flex items-center">
                          <Heart className="w-4 h-4 mr-1" />
                          {hustle.saves.toLocaleString()} saves
                        </div>
                      </div>
                    </div>

                    {/* Trend Indicator */}
                    <div className="flex-shrink-0 text-center">
                      <div className="flex items-center justify-center mb-1">
                        {getTrendIcon(hustle.trend)}
                        <span className={`ml-1 text-sm font-medium ${
                          hustle.trend === 'up' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {hustle.trendPercentage}%
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">vs last week</div>
                    </div>

                    {/* Action Button */}
                    <div className="flex-shrink-0">
                      <Button
                        size="sm"
                        rightIcon={<ArrowRight className="w-4 h-4" />}
                        onClick={() => handleLearnMore(hustle)}
                      >
                        Learn More
                      </Button>
                    </div>
                  </div>
                </div>
              </Card.Content>
            </Card>
          ))}
        </div>

        {/* Trending Insights */}
        <Card className="mt-12 p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Trending Insights</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-4">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Rising Categories</h3>
              <p className="text-sm text-gray-600">Technology and AI-related hustles are seeing 45% growth this month</p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                <Clock className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Peak Hours</h3>
              <p className="text-sm text-gray-600">Most activity happens between 7-9 PM when people plan their side hustles</p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-4">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Community Growth</h3>
              <p className="text-sm text-gray-600">2,300 new entrepreneurs joined this week, 85% are beginners</p>
            </div>
          </div>
        </Card>

        {/* Detailed Hustle Modal */}
        {showDetailModal && selectedHustle && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              {/* Modal Header */}
              <div className="sticky top-0 bg-white border-b border-gray-200 p-6 z-10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold mr-4 ${
                      selectedHustle.rank === 1 ? 'bg-yellow-100 text-yellow-800' :
                      selectedHustle.rank === 2 ? 'bg-gray-100 text-gray-800' :
                      selectedHustle.rank === 3 ? 'bg-orange-100 text-orange-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      #{selectedHustle.rank}
                    </div>
                    <div>
                      <div className="flex items-center gap-3">
                        <h2 className="text-2xl font-bold text-gray-900">{selectedHustle.title}</h2>
                        {getVelocityBadge(selectedHustle.velocity)}
                      </div>
                      <div className="flex items-center gap-4 mt-2">
                        <Badge variant="outline">{selectedHustle.category}</Badge>
                        <div className="flex items-center text-sm text-gray-600">
                          {getTrendIcon(selectedHustle.trend)}
                          <span className={`ml-1 font-medium ${
                            selectedHustle.trend === 'up' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {selectedHustle.trendPercentage}% vs last week
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={handleCloseDetail}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6">
                {/* Text-to-Speech Controls */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Volume2 className="w-5 h-5 text-gray-600" />
                      <h3 className="text-lg font-semibold text-gray-900">Listen to Hustle Guide</h3>
                      {isPlaying && (
                        <div className="flex items-center gap-2 text-sm text-blue-600">
                          <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                          <span>Playing</span>
                        </div>
                      )}
                    </div>

                    {/* Volume and Rate Controls */}
                    <div className="flex items-center gap-3">
                      <button
                        onClick={toggleMute}
                        className="p-2 hover:bg-gray-200 rounded-full transition-colors"
                        title={isMuted ? 'Unmute' : 'Mute'}
                      >
                        {isMuted ? (
                          <VolumeX className="w-4 h-4 text-gray-600" />
                        ) : (
                          <Volume2 className="w-4 h-4 text-gray-600" />
                        )}
                      </button>

                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">Speed:</span>
                        <select
                          value={speechRate}
                          onChange={(e) => handleRateChange(parseFloat(e.target.value))}
                          className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value={0.5}>0.5x</option>
                          <option value={0.75}>0.75x</option>
                          <option value={1}>1x</option>
                          <option value={1.25}>1.25x</option>
                          <option value={1.5}>1.5x</option>
                          <option value={2}>2x</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Main Controls */}
                  <div className="flex items-center gap-3 mb-4">
                    {!isPlaying ? (
                      <Button
                        onClick={startSpeech}
                        className="flex items-center gap-2"
                        size="sm"
                      >
                        <Play className="w-4 h-4" />
                        Play Guide
                      </Button>
                    ) : (
                      <div className="flex items-center gap-2">
                        {!isPaused ? (
                          <Button
                            onClick={pauseSpeech}
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-2"
                          >
                            <Pause className="w-4 h-4" />
                            Pause
                          </Button>
                        ) : (
                          <Button
                            onClick={resumeSpeech}
                            className="flex items-center gap-2"
                            size="sm"
                          >
                            <Play className="w-4 h-4" />
                            Resume
                          </Button>
                        )}

                        <Button
                          onClick={stopSpeech}
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-2"
                        >
                          <Square className="w-4 h-4" />
                          Stop
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* Progress Bar */}
                  {isPlaying && (
                    <div className="mb-4">
                      <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>Progress</span>
                        <span>{Math.round(speechProgress)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${speechProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Current Sentence Display */}
                  {isPlaying && currentSentence && (
                    <div className="p-3 bg-white rounded border border-gray-200">
                      <div className="text-sm text-gray-600 mb-1">Currently reading:</div>
                      <div className="text-gray-800 italic">"{currentSentence}"</div>
                    </div>
                  )}

                  {/* Reading Info */}
                  <div className="text-sm text-gray-600 mt-3">
                    <div className="flex items-center gap-4">
                      <span>📖 Skill Level: {selectedHustle.skillLevel}</span>
                      <span>🎧 Audio length: ~{Math.ceil(getHustleTextContent(selectedHustle).length / 200)} minutes</span>
                    </div>
                  </div>
                </div>

                {/* Key Metrics */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 p-4 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{selectedHustle.potentialEarnings}</div>
                    <div className="text-sm text-gray-600">Potential Earnings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedHustle.timeCommitment}</div>
                    <div className="text-sm text-gray-600">Time Commitment</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{selectedHustle.skillLevel}</div>
                    <div className="text-sm text-gray-600">Skill Level</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{selectedHustle.averageTimeToProfit}</div>
                    <div className="text-sm text-gray-600">Time to Profit</div>
                  </div>
                </div>

                {/* Trending Stats */}
                <div className="grid grid-cols-3 gap-4 mb-8 p-4 bg-blue-50 rounded-lg">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-2">
                      <Eye className="w-5 h-5 text-blue-500 mr-1" />
                      <span className="font-bold text-blue-600">{selectedHustle.views.toLocaleString()}</span>
                    </div>
                    <div className="text-sm text-gray-600">Views</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-2">
                      <Heart className="w-5 h-5 text-red-500 mr-1" />
                      <span className="font-bold text-red-600">{selectedHustle.saves.toLocaleString()}</span>
                    </div>
                    <div className="text-sm text-gray-600">Saves</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-2">
                      <TrendingUp className="w-5 h-5 text-green-500 mr-1" />
                      <span className="font-bold text-green-600">{selectedHustle.marketDemand}</span>
                    </div>
                    <div className="text-sm text-gray-600">Market Demand</div>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">About This Hustle</h3>
                  <p className="text-gray-700 leading-relaxed">{selectedHustle.detailedDescription}</p>
                </div>

                {/* Requirements */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Requirements</h3>
                  <ul className="space-y-2">
                    {selectedHustle.requirements.map((req, index) => (
                      <li key={index} className="flex items-center text-gray-700">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Pros and Cons */}
                <div className="grid md:grid-cols-2 gap-8 mb-8">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                      <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
                        <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      Pros
                    </h3>
                    <ul className="space-y-2">
                      {selectedHustle.pros.map((pro, index) => (
                        <li key={index} className="flex items-center text-gray-700">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                          {pro}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                      <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-2">
                        <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                      Cons
                    </h3>
                    <ul className="space-y-2">
                      {selectedHustle.cons.map((con, index) => (
                        <li key={index} className="flex items-center text-gray-700">
                          <div className="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                          {con}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Success Tips */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Success Tips</h3>
                  <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                    <ul className="space-y-2">
                      {selectedHustle.successTips.map((tip, index) => (
                        <li key={index} className="flex items-start text-gray-700">
                          <div className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                            <span className="text-yellow-600 font-bold text-sm">{index + 1}</span>
                          </div>
                          {tip}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4 pt-6 border-t border-gray-200">
                  <Button className="flex-1">
                    Get Started Now
                  </Button>
                  <Button variant="outline" className="flex-1">
                    Save for Later
                  </Button>
                  <Button variant="outline">
                    Share
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Chatbot */}
        <Chatbot
          currentPage="trending"
          availableContent={selectedHustle ? [selectedHustle] : []}
          allContent={allHustles}
        />
      </div>
    </div>
  );
};

export default TrendingPage;
