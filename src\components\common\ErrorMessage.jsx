import React from 'react';
import { AlertCircle, X, RefreshCw } from 'lucide-react';
import clsx from 'clsx';
import Button from '../ui/Button';

const ErrorMessage = ({
  title = 'Something went wrong',
  message,
  variant = 'error',
  showIcon = true,
  dismissible = false,
  onDismiss,
  onRetry,
  retryText = 'Try Again',
  className,
}) => {
  const variants = {
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800',
  };
  
  const iconColors = {
    error: 'text-red-500',
    warning: 'text-yellow-500',
    info: 'text-blue-500',
  };
  
  const containerClasses = clsx(
    'rounded-lg border p-4',
    variants[variant],
    className
  );
  
  return (
    <div className={containerClasses}>
      <div className="flex">
        {showIcon && (
          <div className="flex-shrink-0">
            <AlertCircle className={clsx('w-5 h-5', iconColors[variant])} />
          </div>
        )}
        
        <div className={clsx('flex-1', showIcon && 'ml-3')}>
          <h3 className="text-sm font-medium">{title}</h3>
          {message && (
            <div className="mt-1 text-sm opacity-90">
              {message}
            </div>
          )}
          
          {onRetry && (
            <div className="mt-3">
              <Button
                size="sm"
                variant="outline"
                onClick={onRetry}
                leftIcon={<RefreshCw className="w-4 h-4" />}
              >
                {retryText}
              </Button>
            </div>
          )}
        </div>
        
        {dismissible && onDismiss && (
          <div className="flex-shrink-0 ml-3">
            <button
              onClick={onDismiss}
              className={clsx(
                'inline-flex rounded-md p-1.5 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-offset-2',
                iconColors[variant]
              )}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ErrorMessage;
