// Chatbot Service for Content Summarization
export class ChatbotService {
  // Summarize blog post into 7 key points
  static summarizeBlogPost(post) {
    if (!post) return null;
    
    const cleanContent = this.cleanHtmlContent(post.content || '');
    const summary = this.generateSevenPoints(cleanContent, post.title, 'blog');
    
    return {
      type: 'blog',
      title: post.title,
      summary: summary,
      link: `/blog/${post.id}`,
      readTime: post.readTime,
      category: post.category,
      author: post.author
    };
  }

  // Summarize trending hustle into 7 key insights
  static summarizeTrendingHustle(hustle) {
    if (!hustle) return null;
    
    const cleanContent = this.cleanHtmlContent(hustle.detailedDescription || hustle.description || '');
    const summary = this.generateSevenPoints(cleanContent, hustle.title, 'hustle');
    
    return {
      type: 'hustle',
      title: hustle.title,
      summary: summary,
      link: `#hustle-${hustle.id}`,
      earnings: hustle.potentialEarnings,
      difficulty: hustle.skillLevel || hustle.difficulty,
      timeCommitment: hustle.timeCommitment
    };
  }

  // Summarize success story into 7 key takeaways
  static summarizeSuccessStory(story) {
    if (!story) return null;
    
    const cleanContent = this.cleanHtmlContent(story.fullStory || '');
    const summary = this.generateSevenPoints(cleanContent, `${story.name}'s Journey`, 'success');
    
    return {
      type: 'success',
      title: `${story.name}'s ${story.hustle} Success Story`,
      summary: summary,
      link: `#story-${story.id}`,
      earnings: story.earnings,
      timeframe: story.timeframe,
      location: story.location
    };
  }

  // Clean HTML content and extract text
  static cleanHtmlContent(htmlContent) {
    if (!htmlContent) return '';
    
    // Remove HTML tags
    const cleanText = htmlContent
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .trim();
    
    return cleanText;
  }

  // Generate 7 key points based on content type
  static generateSevenPoints(content, title, type) {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 15);

    const templates = {
      blog: [
        { title: "Main Topic" },
        { title: "Key Strategy" },
        { title: "Implementation Steps" },
        { title: "Benefits & Advantages" },
        { title: "Challenges to Consider" },
        { title: "Expert Tips" },
        { title: "Actionable Takeaway" }
      ],
      hustle: [
        { title: "Business Overview" },
        { title: "Getting Started" },
        { title: "Required Skills/Tools" },
        { title: "Earning Potential" },
        { title: "Time Investment" },
        { title: "Success Strategies" },
        { title: "Next Steps" }
      ],
      success: [
        { title: "Starting Point" },
        { title: "Key Challenge" },
        { title: "Breakthrough Moment" },
        { title: "Growth Strategy" },
        { title: "Current Success" },
        { title: "Lessons Learned" },
        { title: "Advice for Others" }
      ]
    };

    const pointTemplates = templates[type] || templates.blog;

    return pointTemplates.map((template, index) => ({
      point: index + 1,
      title: template.title,
      content: this.extractPointContent(sentences, index, sentences.length)
    }));
  }

  // Extract relevant content for each point
  static extractPointContent(sentences, pointIndex, totalSentences) {
    if (sentences.length === 0) {
      return "Key insight extracted from the content.";
    }

    // Distribute sentences across 7 points
    const sentencesPerPoint = Math.max(1, Math.floor(totalSentences / 7));
    const startIndex = pointIndex * sentencesPerPoint;
    const endIndex = Math.min(startIndex + sentencesPerPoint, totalSentences);
    
    const relevantSentences = sentences.slice(startIndex, endIndex);
    
    if (relevantSentences.length > 0) {
      // Take the most substantial sentence
      const bestSentence = relevantSentences
        .filter(s => s.length > 20)
        .sort((a, b) => b.length - a.length)[0] || relevantSentences[0];
      
      return bestSentence.trim() + '.';
    }
    
    return "Important insight from this section of the content.";
  }

  // Process user input and determine intent
  static processUserInput(input, availableContent, allContent = []) {
    const lowerInput = input.toLowerCase();

    // Check for summarization requests
    const summaryKeywords = ['summarize', 'summary', 'points', 'key points', '7 points', 'breakdown', 'main points'];
    const isSummaryRequest = summaryKeywords.some(keyword => lowerInput.includes(keyword));

    // Check for search requests
    const searchKeywords = ['find', 'search', 'look for', 'show me', 'where is', 'about'];
    const isSearchRequest = searchKeywords.some(keyword => lowerInput.includes(keyword));

    if (isSummaryRequest) {
      // If content is available, summarize it
      if (availableContent && availableContent.length > 0) {
        return {
          intent: 'summarize',
          content: availableContent[0]
        };
      }

      // If no content available, try to find specific content to summarize
      const foundContent = this.findSpecificContent(input, allContent);
      if (foundContent) {
        return {
          intent: 'summarize',
          content: foundContent
        };
      }
    }

    if (isSearchRequest) {
      const searchResults = this.searchContent(input, allContent);
      if (searchResults.length > 0) {
        return {
          intent: 'search',
          results: searchResults
        };
      }
    }

    return {
      intent: 'general',
      content: null
    };
  }

  // Find specific content based on user request
  static findSpecificContent(input, allContent) {
    if (!allContent || allContent.length === 0) return null;

    const lowerInput = input.toLowerCase();

    // Try to find content by title, author, or keywords
    return allContent.find(item => {
      const title = (item.title || '').toLowerCase();
      const author = (item.author || item.name || '').toLowerCase();
      const category = (item.category || item.hustle || '').toLowerCase();

      return title.includes(lowerInput.replace(/summarize|summary|points|key points|7 points|breakdown|main points/g, '').trim()) ||
             author.includes(lowerInput) ||
             category.includes(lowerInput) ||
             lowerInput.includes(title.split(' ')[0]) ||
             lowerInput.includes(author.split(' ')[0]);
    });
  }

  // Search content based on user query
  static searchContent(input, allContent) {
    if (!allContent || allContent.length === 0) return [];

    const lowerInput = input.toLowerCase();
    const searchTerms = lowerInput
      .replace(/find|search|look for|show me|where is|about/g, '')
      .trim()
      .split(' ')
      .filter(term => term.length > 2);

    if (searchTerms.length === 0) return [];

    return allContent.filter(item => {
      const searchableText = [
        item.title || '',
        item.description || '',
        item.excerpt || '',
        item.author || item.name || '',
        item.category || item.hustle || '',
        (item.tags || []).join(' '),
        item.content || item.fullStory || item.detailedDescription || ''
      ].join(' ').toLowerCase();

      return searchTerms.some(term => searchableText.includes(term));
    }).slice(0, 5); // Limit to 5 results
  }

  // Generate default responses based on page
  static getDefaultResponse(currentPage, input) {
    const lowerInput = input.toLowerCase();

    // Check if user is asking for help or capabilities
    if (lowerInput.includes('help') || lowerInput.includes('what can you do')) {
      return this.getHelpResponse(currentPage);
    }

    const responses = {
      blog: [
        "I can summarize any blog post in 7 key points! Try asking 'summarize this article' or 'find articles about [topic]'.",
        "Want a quick breakdown? I can extract the 7 most important insights from any blog post or help you find specific articles.",
        "I'm here to help you understand articles better. Ask me for a summary or search for specific topics!"
      ],
      trending: [
        "I can break down any trending hustle into 7 actionable insights! Ask me to 'summarize this hustle' or 'find hustles about [topic]'.",
        "Want to understand opportunities better? I can give you 7 key points about any hustle or help you search for specific ones.",
        "I can help you analyze hustles! Ask me for a summary or search for specific business opportunities."
      ],
      success: [
        "I can summarize any success story in 7 key takeaways! Ask me to 'summarize this story' or 'find stories about [topic]'.",
        "Want the main lessons? I can extract 7 key insights from any success journey or help you find specific stories.",
        "I can help you learn from success stories! Ask me for a summary or search for specific success journeys."
      ]
    };

    const pageResponses = responses[currentPage] || responses.blog;
    return pageResponses[Math.floor(Math.random() * pageResponses.length)];
  }

  // Get help response with capabilities
  static getHelpResponse(currentPage) {
    const capabilities = {
      blog: "📚 **Blog Articles**",
      trending: "🔥 **Trending Hustles**",
      success: "🏆 **Success Stories**"
    };

    const currentCapability = capabilities[currentPage] || capabilities.blog;

    return `I can help you with ${currentCapability}:

**📋 Summarization:**
• "Summarize this article/hustle/story"
• "Give me 7 key points"
• "Break down this content"

**🔍 Search & Find:**
• "Find articles about marketing"
• "Search for freelancing stories"
• "Show me hustles about e-commerce"

**🎯 Specific Content:**
• "Summarize Alex's story"
• "Find the dropshipping article"
• "Show me writing tutorials"

Just ask me anything and I'll help you find or understand the content!`;
  }

  // Format search results for display
  static formatSearchResults(results, currentPage) {
    if (!results || results.length === 0) {
      return {
        title: "🔍 No Results Found",
        content: "I couldn't find any content matching your search. Try different keywords or ask me for help with what I can do!",
        isHTML: false
      };
    }

    const typeLabels = {
      blog: 'Articles',
      trending: 'Hustles',
      success: 'Success Stories'
    };

    const typeLabel = typeLabels[currentPage] || 'Content';

    return {
      title: `🔍 Found ${results.length} ${typeLabel}`,
      content: this.generateSearchResultsHTML(results, currentPage),
      isHTML: true
    };
  }

  // Generate HTML for search results
  static generateSearchResultsHTML(results, currentPage) {
    const getResultLink = (item, page) => {
      switch (page) {
        case 'blog':
          return `/blog/${item.id}`;
        case 'trending':
          return `#hustle-${item.id}`;
        case 'success':
          return `#story-${item.id}`;
        default:
          return '#';
      }
    };

    const getResultMeta = (item, page) => {
      switch (page) {
        case 'blog':
          return `${item.readTime || '5 min read'} • ${item.category || 'Article'}`;
        case 'trending':
          return `${item.potentialEarnings || 'Varies'} • ${item.skillLevel || item.difficulty || 'Intermediate'}`;
        case 'success':
          return `${item.earnings || 'Success Story'} • ${item.timeframe || 'Journey'}`;
        default:
          return '';
      }
    };

    return `
      <div class="search-results">
        <div class="space-y-3">
          ${results.map(item => `
            <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors">
              <div class="flex items-start justify-between gap-3">
                <div class="flex-1">
                  <h4 class="font-semibold text-gray-900 text-sm mb-1">
                    ${item.title || item.name + "'s " + (item.hustle || 'Story')}
                  </h4>
                  <p class="text-gray-600 text-xs mb-2 line-clamp-2">
                    ${(item.excerpt || item.description || item.story || '').substring(0, 100)}...
                  </p>
                  <div class="text-xs text-gray-500">
                    ${getResultMeta(item, currentPage)}
                  </div>
                </div>
                <div class="flex flex-col gap-1">
                  <button onclick="window.location.href='${getResultLink(item, currentPage)}'"
                          class="text-blue-600 hover:text-blue-800 text-xs font-medium px-2 py-1 bg-blue-50 rounded">
                    View
                  </button>
                  <button onclick="navigator.clipboard.writeText('Summarize ${item.title || item.name + "'s story"}')"
                          class="text-green-600 hover:text-green-800 text-xs font-medium px-2 py-1 bg-green-50 rounded">
                    Summarize
                  </button>
                </div>
              </div>
            </div>
          `).join('')}
        </div>

        <div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <p class="text-blue-700 text-sm">
            💡 <strong>Tip:</strong> Click "Summarize" to copy the command, then paste it here to get 7 key points from any content!
          </p>
        </div>
      </div>
    `;
  }

  // Format summary response for display
  static formatSummaryResponse(summaryData) {
    if (!summaryData) return null;
    
    const typeLabels = {
      blog: 'Article',
      hustle: 'Hustle Guide', 
      success: 'Success Story'
    };
    
    const typeLabel = typeLabels[summaryData.type] || 'Content';
    
    return {
      title: `📋 ${summaryData.title}`,
      subtitle: `7 Key Points from this ${typeLabel}`,
      points: summaryData.summary,
      link: summaryData.link,
      metadata: this.getMetadata(summaryData)
    };
  }

  // Get metadata for different content types
  static getMetadata(summaryData) {
    switch (summaryData.type) {
      case 'blog':
        return {
          readTime: summaryData.readTime,
          category: summaryData.category,
          author: summaryData.author
        };
      case 'hustle':
        return {
          earnings: summaryData.earnings,
          difficulty: summaryData.difficulty,
          timeCommitment: summaryData.timeCommitment
        };
      case 'success':
        return {
          earnings: summaryData.earnings,
          timeframe: summaryData.timeframe,
          location: summaryData.location
        };
      default:
        return {};
    }
  }
}
