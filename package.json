{"name": "hustlegpt", "private": true, "version": "1.0.0", "description": "AI-Powered Side Hustle Discovery Platform", "author": "HustleGPT Team", "homepage": "https://hustlegpt.netlify.app", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "ogl": "^1.0.11", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.3", "uuid": "^11.1.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.3"}}