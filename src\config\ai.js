// AI API Configuration
// OpenRouter Llama Integration

export const AI_CONFIG = {
  // REAL API INTEGRATION - ENABLED
  enabled: true,

  // OpenRouter API endpoint
  endpoint: 'https://openrouter.ai/api/v1/chat/completions',

  // Your OpenRouter API key
  apiKey: '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',

  // Nvidia Llama model (FREE)
  model: 'nvidia/llama-3.3-nemotron-super-49b-v1:free',

  // Optimized settings for this model
  maxTokens: 3000,
  temperature: 0.8,

  // Timeout in milliseconds (extended for AI-only mode)
  timeout: 120000, // 2 minutes for thorough AI processing

  // OpenRouter specific settings
  httpReferer: 'https://hustlegpt.com',
  xTitle: 'HustleGPT AI Finder',
};

// Validation function to check if AI is properly configured
export const validateAIConfig = () => {
  if (!AI_CONFIG.enabled) {
    return { valid: false, error: 'AI is disabled in configuration' };
  }

  if (!AI_CONFIG.apiKey || AI_CONFIG.apiKey === 'YOUR_OPENROUTER_API_KEY_HERE') {
    return { valid: false, error: 'OpenRouter API key not configured' };
  }

  if (!AI_CONFIG.endpoint) {
    return { valid: false, error: 'API endpoint not configured' };
  }

  if (!AI_CONFIG.model) {
    return { valid: false, error: 'AI model not configured' };
  }

  return { valid: true };
};


