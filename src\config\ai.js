// AI API Configuration
// OpenRouter Llama Integration

export const AI_CONFIG = {
  // REAL API INTEGRATION - ENABLED
  enabled: true,

  // OpenRouter API endpoint
  endpoint: 'https://openrouter.ai/api/v1/chat/completions',

  // API key from environment variables
  apiKey: import.meta.env.VITE_AI_API_KEY || import.meta.env.VITE_OPENROUTER_API_KEY,

  // Multiple model fallbacks (in order of preference)
  models: [
    'meta-llama/llama-3.1-8b-instruct:free',           // Most reliable free model
    'microsoft/phi-3-mini-128k-instruct:free',         // Fast and reliable
    'google/gemma-2-9b-it:free',                       // Google's free model
    'mistralai/mistral-7b-instruct:free',              // Mistral free model
    'nvidia/llama-3.3-nemotron-super-49b-v1:free',    // Original (if available)
  ],

  // Primary model (first in fallback list)
  model: 'meta-llama/llama-3.1-8b-instruct:free',

  // Optimized settings
  maxTokens: 2000,
  temperature: 0.7,

  // Timeout in milliseconds
  timeout: 60000, // 1 minute timeout

  // OpenRouter specific settings
  httpReferer: import.meta.env.VITE_APP_URL || 'https://hustlegpt.netlify.app',
  xTitle: 'HustleGPT AI Finder',
};

// Validation function to check if AI is properly configured
export const validateAIConfig = () => {
  if (!AI_CONFIG.enabled) {
    return { valid: false, error: 'AI is disabled in configuration' };
  }

  if (!AI_CONFIG.apiKey || AI_CONFIG.apiKey === 'YOUR_OPENROUTER_API_KEY_HERE' || AI_CONFIG.apiKey === '') {
    return {
      valid: false,
      error: 'OpenRouter API key not configured. Please set VITE_AI_API_KEY in your environment variables.'
    };
  }

  if (!AI_CONFIG.endpoint) {
    return { valid: false, error: 'API endpoint not configured' };
  }

  if (!AI_CONFIG.model && (!AI_CONFIG.models || AI_CONFIG.models.length === 0)) {
    return { valid: false, error: 'AI model not configured' };
  }

  return { valid: true };
};

// Get next available model for fallback
export const getNextModel = (currentModelIndex = 0) => {
  if (currentModelIndex >= AI_CONFIG.models.length) {
    return null; // No more models to try
  }
  return AI_CONFIG.models[currentModelIndex];
};


