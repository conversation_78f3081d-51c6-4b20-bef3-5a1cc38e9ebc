import React from 'react';
import { Loader2 } from 'lucide-react';
import clsx from 'clsx';

const LoadingSpinner = ({
  size = 'md',
  color = 'blue',
  text,
  fullScreen = false,
  className,
}) => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };
  
  const colors = {
    blue: 'text-blue-600',
    gray: 'text-gray-600',
    white: 'text-white',
    green: 'text-green-600',
    red: 'text-red-600',
  };
  
  const spinnerClasses = clsx(
    'animate-spin',
    sizes[size],
    colors[color]
  );
  
  const containerClasses = clsx(
    'flex items-center justify-center',
    fullScreen && 'min-h-screen',
    className
  );
  
  return (
    <div className={containerClasses}>
      <div className="text-center">
        <Loader2 className={spinnerClasses} />
        {text && (
          <p className="mt-2 text-sm text-gray-600">{text}</p>
        )}
      </div>
    </div>
  );
};

export default LoadingSpinner;
