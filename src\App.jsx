import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { ROUTES } from './constants/routes';
import ProtectedRoute from './components/common/ProtectedRoute';
import Layout from './components/layout/Layout';

// Import all pages
import {
  LandingPage,
  HomePage,
  LoginPage,
  SignupPage,
  DashboardPage,
  AIFinderPage,
  BlogPage,
  ContactPage,
  HustleDetailPage,
  SuccessStoriesPage,
  TrendingPage,
  RedirectPage,
  ProfilePage,
  SettingsPage,
  ProgressPage,
  NotFoundPage
} from './pages';

const AppContent = () => {
  const location = useLocation();
  const isLandingPage = location.pathname === ROUTES.LANDING;
  const isAuthPage = location.pathname === ROUTES.LOGIN || location.pathname === ROUTES.SIGNUP;

  return (
    <Layout showHeader={!isLandingPage && !isAuthPage} showFooter={!isLandingPage && !isAuthPage}>
      <Routes>
            {/* Public Routes - ONLY Landing Page */}
            <Route path={ROUTES.LANDING} element={<LandingPage />} />

            {/* Auth Routes (only for non-authenticated users) */}
            <Route
              path={ROUTES.LOGIN}
              element={
                <ProtectedRoute requireAuth={false}>
                  <LoginPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.SIGNUP}
              element={
                <ProtectedRoute requireAuth={false}>
                  <SignupPage />
                </ProtectedRoute>
              }
            />

            {/* Protected Routes - ALL PAGES REQUIRE AUTHENTICATION */}
            <Route
              path={ROUTES.HOME}
              element={
                <ProtectedRoute requireAuth={true}>
                  <HomePage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.DASHBOARD}
              element={
                <ProtectedRoute requireAuth={true}>
                  <DashboardPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.AI_FINDER}
              element={
                <ProtectedRoute requireAuth={true}>
                  <AIFinderPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.BLOG}
              element={
                <ProtectedRoute requireAuth={true}>
                  <BlogPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.BLOG_POST}
              element={
                <ProtectedRoute requireAuth={true}>
                  <BlogPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.CONTACT}
              element={
                <ProtectedRoute requireAuth={true}>
                  <ContactPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.HUSTLE_DETAIL}
              element={
                <ProtectedRoute requireAuth={true}>
                  <HustleDetailPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.SUCCESS_STORIES}
              element={
                <ProtectedRoute requireAuth={true}>
                  <SuccessStoriesPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.TRENDING}
              element={
                <ProtectedRoute requireAuth={true}>
                  <TrendingPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.REDIRECT}
              element={
                <ProtectedRoute requireAuth={true}>
                  <RedirectPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.PROFILE}
              element={
                <ProtectedRoute requireAuth={true}>
                  <ProfilePage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.SETTINGS}
              element={
                <ProtectedRoute requireAuth={true}>
                  <SettingsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path={ROUTES.PROGRESS}
              element={
                <ProtectedRoute requireAuth={true}>
                  <ProgressPage />
                </ProtectedRoute>
              }
            />

            {/* Landing page is now the root route */}

            {/* 404 Not Found - ALSO PROTECTED */}
            <Route
              path={ROUTES.NOT_FOUND}
              element={
                <ProtectedRoute requireAuth={true}>
                  <NotFoundPage />
                </ProtectedRoute>
              }
            />
          </Routes>
        </Layout>
      );
    };

    function App() {
      return (
        <AuthProvider>
          <Router>
            <AppContent />
          </Router>
        </AuthProvider>
      );
    }

    export default App;
