import { useState, useEffect, useCallback } from 'react';
import { Link, useParams } from 'react-router-dom';
import { Search, Calendar, Clock, User, Tag, ArrowRight, TrendingUp, Star, Play, Pause, Square, Volume2, VolumeX } from 'lucide-react';
import { But<PERSON>, Card, Badge, Input } from '../components/ui';
import { LoadingSpinner, BlogImage } from '../components/common';
import { blogAPI } from '../services/api';
import Chatbot from '../components/chatbot/Chatbot';
import { formatDate } from '../utils/helpers';

const BlogPage = () => {
  const [posts, setPosts] = useState([]);
  const [featuredPosts, setFeaturedPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  // Text-to-Speech state
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentUtterance, setCurrentUtterance] = useState(null);
  const [speechProgress, setSpeechProgress] = useState(0);
  const [speechRate, setSpeechRate] = useState(1);
  const [speechVolume, setSpeechVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [selectedPost, setSelectedPost] = useState(null);

  const { id } = useParams();

  // Dynamic categories based on actual blog posts
  const categories = ['All', ...new Set(posts.map(post => post.category))].sort();

  useEffect(() => {
    loadBlogPosts();
  }, []);

  const loadBlogPosts = async () => {
    setIsLoading(true);
    try {
      const [allPosts, featured] = await Promise.all([
        blogAPI.getAll(),
        blogAPI.getFeatured()
      ]);
      setPosts(allPosts);
      setFeaturedPosts(featured);
    } catch (error) {
      console.error('Error loading blog posts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredPosts = posts.filter(post => {
    // Enhanced search functionality
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = !searchTerm ||
      post.title.toLowerCase().includes(searchLower) ||
      post.excerpt.toLowerCase().includes(searchLower) ||
      post.author.toLowerCase().includes(searchLower) ||
      post.content.toLowerCase().includes(searchLower) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchLower));

    // Category filtering
    const matchesCategory = selectedCategory === 'all' ||
      post.category.toLowerCase() === selectedCategory;

    return matchesSearch && matchesCategory;
  }).sort((a, b) => {
    // Sort functionality
    switch (sortBy) {
      case 'newest':
        return new Date(b.publishDate) - new Date(a.publishDate);
      case 'oldest':
        return new Date(a.publishDate) - new Date(b.publishDate);
      case 'title':
        return a.title.localeCompare(b.title);
      case 'author':
        return a.author.localeCompare(b.author);
      case 'readTime':
        return parseInt(a.readTime) - parseInt(b.readTime);
      default:
        return 0;
    }
  });

  if (isLoading) {
    return <LoadingSpinner fullScreen text="Loading blog posts..." />;
  }

  // If viewing a specific post
  if (id) {
    return <BlogPostView postId={id} allPosts={posts} />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            HustleGPT Blog
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Expert tips, success stories, and guides to help you build your perfect side hustle
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search articles by title, content, or author..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<Search className="w-5 h-5" />}
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Sort Dropdown */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 whitespace-nowrap">Sort by:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="title">Title A-Z</option>
                  <option value="author">Author A-Z</option>
                  <option value="readTime">Read Time</option>
                </select>
              </div>

              {/* Category Filters */}
              <div className="flex flex-wrap gap-2">
              {categories.map((category) => {
                const isActive = selectedCategory === category.toLowerCase();
                const categoryCount = category === 'All'
                  ? posts.length
                  : posts.filter(post => post.category === category).length;

                return (
                  <Button
                    key={category}
                    variant={isActive ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory(category.toLowerCase())}
                    className={`transition-all duration-200 ${
                      isActive ? 'shadow-md' : 'hover:shadow-sm'
                    }`}
                  >
                    {category}
                    <Badge
                      className={`ml-2 text-xs ${
                        isActive
                          ? 'bg-white/20 text-white'
                          : 'bg-gray-100 text-gray-600'
                      }`}
                    >
                      {categoryCount}
                    </Badge>
                  </Button>
                );
              })}
              </div>
            </div>
          </div>

          {/* Active Filters Display */}
          {(searchTerm || selectedCategory !== 'all') && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>Active filters:</span>
                {searchTerm && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    Search: "{searchTerm}"
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-1 hover:text-red-500"
                    >
                      ×
                    </button>
                  </Badge>
                )}
                {selectedCategory !== 'all' && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    Category: {categories.find(cat => cat.toLowerCase() === selectedCategory)}
                    <button
                      onClick={() => setSelectedCategory('all')}
                      className="ml-1 hover:text-red-500"
                    >
                      ×
                    </button>
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('all');
                  }}
                  className="text-xs"
                >
                  Clear all
                </Button>
              </div>
            </div>
          )}
        </Card>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && selectedCategory === 'all' && !searchTerm && (
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <Star className="w-6 h-6 text-yellow-500 mr-2" />
              <h2 className="text-2xl font-bold text-gray-900">Featured Articles</h2>
            </div>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredPosts.map((post) => (
                <Card key={post.id} className="hover:shadow-lg transition-shadow">
                  {/* Featured Post Image */}
                  <BlogImage
                    src={post.image}
                    alt={post.imageAlt || post.title}
                    category={post.category}
                    fallbackGradient="from-blue-500 to-purple-600"
                  />
                  <Card.Content className="p-6">
                    <Badge variant="primary" className="mb-3">{post.category}</Badge>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center">
                        <User className="w-4 h-4 mr-1" />
                        {post.author}
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {post.readTime}
                      </div>
                    </div>
                    <Link to={`/blog/${post.id}`}>
                      <Button size="sm" rightIcon={<ArrowRight className="w-4 h-4" />}>
                        Read More
                      </Button>
                    </Link>
                  </Card.Content>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* All Posts */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">
              {searchTerm ? `Search Results (${filteredPosts.length})` : 'Latest Articles'}
            </h2>
            <div className="flex items-center text-sm text-gray-600">
              <TrendingUp className="w-4 h-4 mr-1" />
              {filteredPosts.length} articles
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPosts.map((post) => (
            <Card key={post.id} className="hover:shadow-lg transition-shadow">
              {/* Regular Post Image */}
              <BlogImage
                src={post.image}
                alt={post.imageAlt || post.title}
                category={post.category}
                fallbackGradient="from-gray-400 to-gray-600"
              />
              <Card.Content className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <Badge variant="outline">{post.category}</Badge>
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="w-3 h-3 mr-1" />
                    {formatDate(post.publishDate)}
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                  {post.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center">
                    <User className="w-4 h-4 mr-1" />
                    {post.author}
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {post.readTime}
                  </div>
                </div>
                <div className="flex flex-wrap gap-1 mb-4">
                  {post.tags?.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                <Link to={`/blog/${post.id}`}>
                  <Button size="sm" fullWidth rightIcon={<ArrowRight className="w-4 h-4" />}>
                    Read Article
                  </Button>
                </Link>
              </Card.Content>
            </Card>
          ))}
        </div>

        {filteredPosts.length === 0 && (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No articles found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or filters</p>
            <Button onClick={() => { setSearchTerm(''); setSelectedCategory('all'); }}>
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

// Blog Post View Component
const BlogPostView = ({ postId, allPosts = [] }) => {
  const [post, setPost] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Text-to-Speech state
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentUtterance, setCurrentUtterance] = useState(null);
  const [speechProgress, setSpeechProgress] = useState(0);
  const [speechRate, setSpeechRate] = useState(1);
  const [speechVolume, setSpeechVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [currentSentence, setCurrentSentence] = useState('');

  const loadPost = useCallback(async () => {
    setIsLoading(true);
    try {
      const postData = await blogAPI.getById(postId);
      setPost(postData);
    } catch (error) {
      console.error('Error loading post:', error);
    } finally {
      setIsLoading(false);
    }
  }, [postId]);

  useEffect(() => {
    loadPost();
  }, [loadPost]);

  // Text-to-Speech Functions
  const getTextContent = (post) => {
    if (!post) return '';

    // Extract text from HTML content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = post.content || '';
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Combine title, excerpt, and content
    return `${post.title}. ${post.excerpt}. ${textContent}`;
  };

  const startSpeech = () => {
    if (!post) return;

    // Stop any existing speech
    if (currentUtterance) {
      speechSynthesis.cancel();
    }

    const textToSpeak = getTextContent(post);
    const utterance = new SpeechSynthesisUtterance(textToSpeak);

    // Configure speech settings
    utterance.rate = speechRate;
    utterance.volume = isMuted ? 0 : speechVolume;
    utterance.pitch = 1;

    // Event handlers
    utterance.onstart = () => {
      setIsPlaying(true);
      setIsPaused(false);
      setSpeechProgress(0);
    };

    utterance.onend = () => {
      setIsPlaying(false);
      setIsPaused(false);
      setSpeechProgress(100);
      setCurrentUtterance(null);
      setCurrentSentence('');
    };

    utterance.onerror = () => {
      setIsPlaying(false);
      setIsPaused(false);
      setCurrentUtterance(null);
      setCurrentSentence('');
    };

    utterance.onboundary = (event) => {
      if (event.name === 'sentence') {
        const progress = (event.charIndex / textToSpeak.length) * 100;
        setSpeechProgress(progress);

        // Extract current sentence for display
        const sentences = textToSpeak.split(/[.!?]+/);
        let charCount = 0;
        for (let sentence of sentences) {
          charCount += sentence.length + 1;
          if (charCount > event.charIndex) {
            setCurrentSentence(sentence.trim());
            break;
          }
        }
      }
    };

    setCurrentUtterance(utterance);
    speechSynthesis.speak(utterance);
  };

  const pauseSpeech = () => {
    if (speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
      setIsPaused(true);
    }
  };

  const resumeSpeech = () => {
    if (speechSynthesis.paused) {
      speechSynthesis.resume();
      setIsPaused(false);
    }
  };

  const stopSpeech = () => {
    speechSynthesis.cancel();
    setIsPlaying(false);
    setIsPaused(false);
    setSpeechProgress(0);
    setCurrentUtterance(null);
    setCurrentSentence('');
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    if (currentUtterance) {
      currentUtterance.volume = isMuted ? speechVolume : 0;
    }
  };

  const handleRateChange = (newRate) => {
    setSpeechRate(newRate);
    if (isPlaying && currentUtterance) {
      // Restart with new rate
      const wasPlaying = isPlaying;
      stopSpeech();
      if (wasPlaying) {
        setTimeout(startSpeech, 100);
      }
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (currentUtterance) {
        speechSynthesis.cancel();
      }
    };
  }, [currentUtterance]);

  if (isLoading) {
    return <LoadingSpinner fullScreen text="Loading article..." />;
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Article not found</h2>
          <Link to="/blog">
            <Button>Back to Blog</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <Link to="/blog" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-8">
          <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
          Back to Blog
        </Link>

        <article>
          <header className="mb-8">
            <Badge variant="primary" className="mb-4">{post.category}</Badge>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">{post.title}</h1>
            <div className="flex items-center space-x-6 text-gray-600">
              <div className="flex items-center">
                <User className="w-5 h-5 mr-2" />
                {post.author}
              </div>
              <div className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                {formatDate(post.publishDate)}
              </div>
              <div className="flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                {post.readTime}
              </div>
            </div>
          </header>

          {/* Article Image */}
          <BlogImage
            src={post.image}
            alt={post.imageAlt || post.title}
            category={post.category}
            fallbackGradient="from-blue-500 to-purple-600"
            className="mb-6"
            showHoverEffect={false}
          />

          {/* Text-to-Speech Controls */}
          <div className="mb-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Volume2 className="w-5 h-5 text-gray-600" />
                <h3 className="text-lg font-semibold text-gray-900">Listen to Article</h3>
                {isPlaying && (
                  <div className="flex items-center gap-2 text-sm text-blue-600">
                    <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                    <span>Playing</span>
                  </div>
                )}
              </div>

              {/* Volume and Rate Controls */}
              <div className="flex items-center gap-3">
                <button
                  onClick={toggleMute}
                  className="p-2 hover:bg-gray-200 rounded-full transition-colors"
                  title={isMuted ? 'Unmute' : 'Mute'}
                >
                  {isMuted ? (
                    <VolumeX className="w-4 h-4 text-gray-600" />
                  ) : (
                    <Volume2 className="w-4 h-4 text-gray-600" />
                  )}
                </button>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Speed:</span>
                  <select
                    value={speechRate}
                    onChange={(e) => handleRateChange(parseFloat(e.target.value))}
                    className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={0.5}>0.5x</option>
                    <option value={0.75}>0.75x</option>
                    <option value={1}>1x</option>
                    <option value={1.25}>1.25x</option>
                    <option value={1.5}>1.5x</option>
                    <option value={2}>2x</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Main Controls */}
            <div className="flex items-center gap-3 mb-4">
              {!isPlaying ? (
                <Button
                  onClick={startSpeech}
                  className="flex items-center gap-2"
                  size="sm"
                >
                  <Play className="w-4 h-4" />
                  Play Article
                </Button>
              ) : (
                <div className="flex items-center gap-2">
                  {!isPaused ? (
                    <Button
                      onClick={pauseSpeech}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Pause className="w-4 h-4" />
                      Pause
                    </Button>
                  ) : (
                    <Button
                      onClick={resumeSpeech}
                      className="flex items-center gap-2"
                      size="sm"
                    >
                      <Play className="w-4 h-4" />
                      Resume
                    </Button>
                  )}

                  <Button
                    onClick={stopSpeech}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Square className="w-4 h-4" />
                    Stop
                  </Button>
                </div>
              )}
            </div>

            {/* Progress Bar */}
            {isPlaying && (
              <div className="mb-4">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span>{Math.round(speechProgress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${speechProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* Current Sentence Display */}
            {isPlaying && currentSentence && (
              <div className="p-3 bg-white rounded border border-gray-200">
                <div className="text-sm text-gray-600 mb-1">Currently reading:</div>
                <div className="text-gray-800 italic">"{currentSentence}"</div>
              </div>
            )}

            {/* Reading Info */}
            <div className="text-sm text-gray-600 mt-3">
              <div className="flex items-center gap-4">
                <span>📖 Estimated reading time: {post.readTime}</span>
                <span>🎧 Audio length: ~{Math.ceil(getTextContent(post).length / 200)} minutes</span>
              </div>
            </div>
          </div>

          <div className="prose prose-lg max-w-none">
            <p className="text-xl text-gray-600 mb-8">{post.excerpt}</p>
            <div
              className="text-gray-800 leading-relaxed"
              dangerouslySetInnerHTML={{
                __html: post.content || 'This article content is being loaded...'
              }}
            />
          </div>

          <footer className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex flex-wrap gap-2">
              {post.tags?.map((tag) => (
                <Badge key={tag} variant="outline">
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          </footer>
        </article>

        {/* Chatbot */}
        <Chatbot
          currentPage="blog"
          availableContent={[post]}
          allContent={allPosts}
        />
      </div>
    </div>
  );
};

export default BlogPage;
