import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  <PERSON>R<PERSON>,
  Zap,
  TrendingUp,
  Users,
  BookOpen,
  Star,
  CheckCircle,
  Play,
  DollarSign,
  Clock,
  Target,
  Award
} from 'lucide-react';
import { <PERSON><PERSON>, Card, Badge } from '../components/ui';
import LandingHeader from '../components/layout/LandingHeader';
import { ROUTES } from '../constants/routes';
import Particles from '../components/effects/Particles';

const LandingPage = () => {
  const [activeTestimonial, setActiveTestimonial] = useState(0);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isSliding, setIsSliding] = useState(false);
  const [slideDirection, setSlideDirection] = useState('right');
  const [isHeroVisible, setIsHeroVisible] = useState(false);
  const [animationStage, setAnimationStage] = useState(0);

  // Hero images for rotation
  const heroImages = [
    {
      src: "https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      alt: "Entrepreneurs working on side hustles",
      title: "Start Your Journey",
      subtitle: "Join thousands of successful entrepreneurs"
    },
    {
      src: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80",
      alt: "Team collaboration and success",
      title: "Build Together",
      subtitle: "Collaborate and grow your business"
    },
    {
      src: "https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      alt: "Laptop workspace and productivity",
      title: "Work Smart",
      subtitle: "Optimize your productivity and earnings"
    }
  ];

  // Hero text animation on page load
  useEffect(() => {
    // Trigger initial animation
    setTimeout(() => setIsHeroVisible(true), 100);

    // Stagger text animations
    const stages = [0, 1, 2, 3];
    stages.forEach((stage, index) => {
      setTimeout(() => setAnimationStage(stage), 200 + (index * 300));
    });
  }, []);

  // Auto-rotate images with slide animations
  useEffect(() => {
    const interval = setInterval(() => {
      // Start slide out animation
      setIsSliding(true);
      setSlideDirection('right'); // Always slide to right for auto-rotation

      setTimeout(() => {
        // Change image and start slide in
        setCurrentImageIndex((prevIndex) =>
          prevIndex === heroImages.length - 1 ? 0 : prevIndex + 1
        );

        setTimeout(() => {
          // End slide animation
          setIsSliding(false);
        }, 400);
      }, 400); // Slide out time
    }, 5000); // Change image every 5 seconds

    return () => clearInterval(interval);
  }, [heroImages.length]);

  const features = [
    {
      icon: Zap,
      title: 'AI-Powered Recommendations',
      description: 'Get personalized side hustle suggestions based on your skills, interests, and available time.',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      icon: TrendingUp,
      title: 'Trending Opportunities',
      description: 'Stay ahead with real-time insights on the hottest side hustles and emerging markets.',
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      icon: Users,
      title: 'Community Success',
      description: 'Learn from thousands of entrepreneurs who have built successful side businesses.',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      icon: BookOpen,
      title: 'Expert Guidance',
      description: 'Access comprehensive guides, tips, and strategies from industry experts.',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  const stats = [
    { number: '50K+', label: 'Active Users', icon: Users },
    { number: '1000+', label: 'Hustle Ideas', icon: Target },
    { number: '$10M+', label: 'Earned by Community', icon: DollarSign },
    { number: '95%', label: 'Success Rate', icon: Award }
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Freelance Writer',
      image: null,
      quote: 'HustleGPT helped me discover freelance writing. I went from $0 to $4,000/month in just 6 months!',
      earnings: '$4,000/month',
      timeframe: '6 months'
    },
    {
      name: 'Mike Chen',
      role: 'Online Tutor',
      image: null,
      quote: 'The AI recommendations were spot-on. I found my perfect side hustle in tutoring and love every minute of it.',
      earnings: '$3,500/month',
      timeframe: '4 months'
    },
    {
      name: 'Emma Davis',
      role: 'E-commerce Store Owner',
      image: null,
      quote: 'Started my dropshipping business through HustleGPT. Now I have financial freedom and work on my own terms.',
      earnings: '$8,000/month',
      timeframe: '10 months'
    }
  ];

  const pricingFeatures = [
    'AI-powered hustle recommendations',
    'Access to 1000+ verified opportunities',
    'Community forum and networking',
    'Expert guides and tutorials',
    'Success tracking dashboard',
    'Priority customer support'
  ];

  return (
    <div className="min-h-screen bg-white">
      <LandingHeader />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        {/* 3D Particle Effects Background */}
        <div className="absolute inset-0 z-0">
          <Particles
            particleColors={['#2563eb', '#7c3aed', '#0891b2', '#dc2626', '#ea580c', '#16a34a']}
            particleCount={400}
            particleSpread={12}
            speed={0.3}
            particleBaseSize={150}
            moveParticlesOnHover={true}
            alphaParticles={false}
            disableRotation={false}
            particleHoverFactor={1.2}
            sizeRandomness={1.5}
            cameraDistance={20}
          />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-28 pb-8 z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-left">
              <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight transform transition-all duration-1000 ${
                isHeroVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
              }`}>
                <span className={`block transform transition-all duration-1000 delay-300 ${
                  animationStage >= 0 ? 'translate-x-0 opacity-100' : '-translate-x-8 opacity-0'
                }`}>
                  Turn Your Skills Into
                </span>
                <span className={`text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 block transform transition-all duration-1000 delay-600 ${
                  animationStage >= 1 ? 'translate-x-0 opacity-100 scale-100' : 'translate-x-8 opacity-0 scale-95'
                }`}>
                  Profitable Hustles
                </span>
                <span className={`block transform transition-all duration-1000 delay-900 ${
                  animationStage >= 2 ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
                }`}>
                  with HustleGPT
                </span>
              </h1>

              <p className={`text-xl text-gray-600 mb-8 leading-relaxed transform transition-all duration-1000 delay-1200 ${
                animationStage >= 3 ? 'translate-y-0 opacity-100' : 'translate-y-6 opacity-0'
              }`}>
                Our AI analyzes your skills, interests, and goals to recommend the perfect side hustles.
                Join thousands of entrepreneurs who've discovered their path to financial freedom.
              </p>

              <div className={`flex flex-col sm:flex-row gap-4 mb-8 transform transition-all duration-1000 delay-1500 ${
                animationStage >= 3 ? 'translate-y-0 opacity-100 scale-100' : 'translate-y-8 opacity-0 scale-95'
              }`}>
                <Link to={ROUTES.SIGNUP}>
                  <Button size="xl" rightIcon={<ArrowRight className="w-6 h-6" />}>
                    Start Free Today
                  </Button>
                </Link>
                <Link to={ROUTES.LOGIN}>
                  <Button variant="outline" size="xl" leftIcon={<Play className="w-5 h-5" />}>
                    Watch Demo
                  </Button>
                </Link>
              </div>

              <div className={`flex items-center space-x-6 text-sm text-gray-500 transform transition-all duration-1000 delay-1800 ${
                animationStage >= 3 ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
              }`}>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  <span>100% Free to Start</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  <span>No Credit Card Required</span>
                </div>
              </div>


            </div>

            {/* Right Image Carousel */}
            <div className={`relative transform transition-all duration-1000 delay-500 ${
              isHeroVisible ? 'translate-x-0 opacity-100 scale-100' : 'translate-x-8 opacity-0 scale-95'
            }`}>
              <div className="relative z-10">
                {/* Main Image Container */}
                <div
                  className="relative bg-white rounded-2xl shadow-2xl p-4 transition-all duration-1000 hover:scale-105 hover:shadow-3xl"
                >
                  <div className="relative overflow-hidden rounded-xl h-80 md:h-96">
                    {/* Image Carousel with Slide Animations */}
                    <div className="relative w-full h-full overflow-hidden">
                      {heroImages.map((image, index) => (
                        <div
                          key={index}
                          className={`absolute inset-0 carousel-slide-smooth ${
                            index === currentImageIndex
                              ? 'opacity-100 translate-x-0 scale-100 z-10'
                              : index < currentImageIndex
                              ? 'opacity-0 -translate-x-full scale-95 z-0'
                              : 'opacity-0 translate-x-full scale-95 z-0'
                          } ${
                            isSliding && index === currentImageIndex
                              ? 'carousel-slide-fast'
                              : ''
                          }`}
                          style={{
                            transform: `translateX(${
                              index === currentImageIndex
                                ? '0%'
                                : index < currentImageIndex
                                ? '-100%'
                                : '100%'
                            }) scale(${index === currentImageIndex ? '1' : '0.95'})`,
                            transition: isSliding
                              ? 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
                              : 'all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
                          }}
                        >
                          <img
                            src={image.src}
                            alt={image.alt}
                            className="w-full h-full object-cover hover:scale-110 transition-transform duration-700 ease-out"
                          />

                          {/* Individual overlay for each image */}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                          <div className={`absolute bottom-4 left-4 text-white transition-all duration-500 ${
                            index === currentImageIndex
                              ? 'opacity-100 translate-y-0'
                              : 'opacity-0 translate-y-4'
                          }`}>
                            <h3 className="text-lg font-bold mb-1">{image.title}</h3>
                            <p className="text-sm opacity-90">{image.subtitle}</p>
                          </div>
                        </div>
                      ))}
                    </div>


                    {/* Image indicators */}
                    <div className="absolute bottom-4 right-4 flex space-x-2">
                      {heroImages.map((_, index) => (
                        <button
                          key={index}
                          className={`w-3 h-3 rounded-full transition-all duration-300 hover:scale-125 ${
                            index === currentImageIndex
                              ? 'bg-white scale-125 shadow-lg'
                              : 'bg-white/50 hover:bg-white/75'
                          }`}
                          onClick={() => {
                            if (index !== currentImageIndex) {
                              // Determine slide direction
                              const direction = index > currentImageIndex ? 'right' : 'left';
                              setSlideDirection(direction);

                              // Start slide out animation
                              setIsSliding(true);

                              setTimeout(() => {
                                // Change image
                                setCurrentImageIndex(index);

                                setTimeout(() => {
                                  // End slide animation
                                  setIsSliding(false);
                                }, 400);
                              }, 400);
                            }
                          }}
                          aria-label={`View image ${index + 1}`}
                        />
                      ))}
                    </div>


                  </div>
                </div>

                {/* Floating Success Cards with Enhanced Animations */}
                <div className="absolute -top-4 -left-4 bg-white rounded-lg shadow-lg p-3 transform -rotate-6 hover:rotate-0 hover:scale-110 hover:shadow-xl transition-all duration-500 animate-bounce-slow z-20">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center hover:bg-green-200 transition-colors duration-300">
                      <DollarSign className="w-4 h-4 text-green-600 hover:scale-125 transition-transform duration-300" />
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-gray-900">$3,500/mo</div>
                      <div className="text-xs text-gray-500">Avg. Earnings</div>
                    </div>
                  </div>
                </div>

                <div className="absolute -bottom-4 -right-4 bg-white rounded-lg shadow-lg p-3 transform rotate-6 hover:rotate-0 hover:scale-110 hover:shadow-xl transition-all duration-500 animate-float z-20">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center hover:bg-blue-200 transition-colors duration-300">
                      <Users className="w-4 h-4 text-blue-600 hover:scale-125 transition-transform duration-300" />
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-gray-900">10K+</div>
                      <div className="text-xs text-gray-500">Success Stories</div>
                    </div>
                  </div>
                </div>

                <div className="absolute top-1/2 -left-6 bg-white rounded-lg shadow-lg p-3 transform -translate-y-1/2 rotate-12 hover:rotate-0 hover:scale-110 hover:shadow-xl transition-all duration-500 animate-pulse-slow z-20">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center hover:bg-purple-200 transition-colors duration-300">
                      <Star className="w-4 h-4 text-purple-600 hover:scale-125 transition-transform duration-300" />
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-gray-900">4.9★</div>
                      <div className="text-xs text-gray-500">User Rating</div>
                    </div>
                  </div>
                </div>

                {/* Additional Floating Elements */}
                <div className="absolute top-8 -right-8 bg-gradient-to-r from-orange-400 to-pink-500 text-white rounded-full p-3 transform rotate-45 hover:rotate-0 hover:scale-125 transition-all duration-500 animate-spin-slow z-20">
                  <TrendingUp className="w-5 h-5" />
                </div>

                <div className="absolute bottom-8 -left-8 bg-gradient-to-r from-green-400 to-blue-500 text-white rounded-full p-3 transform -rotate-45 hover:rotate-0 hover:scale-125 transition-all duration-500 animate-bounce-slow z-20">
                  <Target className="w-5 h-5" />
                </div>
              </div>

              {/* Background Decorations */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-2xl transform rotate-3 opacity-10 -z-10"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl transform -rotate-2 opacity-10 -z-10"></div>
            </div>
          </div>

          {/* Trust Indicators - Full Width Spread */}
          <div className={`mt-8 pt-6 border-t border-gray-200/50 transform transition-all duration-1000 delay-2100 ${
            animationStage >= 3 ? 'translate-y-0 opacity-100' : 'translate-y-6 opacity-0'
          }`}>
            <p className="text-center text-sm text-gray-500 mb-4">Trusted by entrepreneurs worldwide</p>
            <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-1">10K+</div>
                <div className="text-sm text-gray-500">Active Users</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-1">500+</div>
                <div className="text-sm text-gray-500">Hustle Ideas</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-1">95%</div>
                <div className="text-sm text-gray-500">Success Rate</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                    <Icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.number}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Succeed
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our platform combines AI technology with real-world insights to help you
              find and build profitable side hustles.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-shadow">
                  <Card.Content className="p-8">
                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-6 ${feature.bgColor}`}>
                      <Icon className={`w-8 h-8 ${feature.color}`} />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">{feature.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  </Card.Content>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Get started in minutes and find your perfect side hustle in 3 simple steps.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 text-white rounded-full text-2xl font-bold mb-6">
                1
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Tell Us About You</h3>
              <p className="text-gray-600">
                Share your skills, interests, available time, and financial goals with our AI system.
              </p>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-green-600 text-white rounded-full text-2xl font-bold mb-6">
                2
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Get AI Recommendations</h3>
              <p className="text-gray-600">
                Our AI analyzes thousands of opportunities to find the perfect matches for your profile.
              </p>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-600 text-white rounded-full text-2xl font-bold mb-6">
                3
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Start Earning</h3>
              <p className="text-gray-600">
                Follow our step-by-step guides and join our community to build your successful side hustle.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Success Stories from Our Community
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Real people, real results. See how HustleGPT has transformed lives.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg">
                <Card.Content className="p-8">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                      {testimonial.name.charAt(0)}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                    </div>
                  </div>

                  <div className="flex mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>

                  <blockquote className="text-gray-700 mb-6 italic">
                    "{testimonial.quote}"
                  </blockquote>

                  <div className="flex justify-between items-center text-sm">
                    <Badge variant="success">{testimonial.earnings}</Badge>
                    <span className="text-gray-500">in {testimonial.timeframe}</span>
                  </div>
                </Card.Content>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-600">
              Start free and upgrade when you're ready to accelerate your success.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Free Plan */}
            <Card className="border-2 border-gray-200">
              <Card.Content className="p-8">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Free</h3>
                  <div className="text-4xl font-bold text-gray-900 mb-2">$0</div>
                  <div className="text-gray-600">Forever free</div>
                </div>

                <ul className="space-y-4 mb-8">
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    <span>Basic AI recommendations</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    <span>Access to 100+ hustles</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    <span>Community forum access</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    <span>Basic guides and tutorials</span>
                  </li>
                </ul>

                <Link to={ROUTES.SIGNUP} className="block">
                  <Button variant="outline" fullWidth size="lg">
                    Get Started Free
                  </Button>
                </Link>
              </Card.Content>
            </Card>

            {/* Pro Plan */}
            <Card className="border-2 border-blue-500 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <Badge variant="primary" className="px-4 py-1">Most Popular</Badge>
              </div>

              <Card.Content className="p-8">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Pro</h3>
                  <div className="text-4xl font-bold text-gray-900 mb-2">$19</div>
                  <div className="text-gray-600">per month</div>
                </div>

                <ul className="space-y-4 mb-8">
                  {pricingFeatures.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link to={ROUTES.SIGNUP} className="block">
                  <Button fullWidth size="lg">
                    Start Pro Trial
                  </Button>
                </Link>
              </Card.Content>
            </Card>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Start Your Side Hustle Journey?
          </h2>
          <p className="text-xl text-blue-100 mb-10 max-w-2xl mx-auto">
            Join thousands of entrepreneurs who have discovered their perfect side hustle
            and are building financial freedom with HustleGPT.
          </p>



          <div className="flex items-center justify-center space-x-6 text-blue-100">
            <div className="flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              <span>Setup in 2 minutes</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" />
              <span>No credit card required</span>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
