import { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Mail, Lock, Eye, EyeOff, ArrowLeft } from 'lucide-react';

import { ErrorMessage } from '../components/common';
import { useAuth } from '../context/AuthContext';
import { ROUTES } from '../constants/routes';
import Particles from '../components/effects/Particles';

const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const from = location.state?.from || ROUTES.DASHBOARD;

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm();

  const onSubmit = async (data) => {
    setIsLoading(true);
    setError('');

    try {
      const result = await login(data.email, data.password);

      if (result.success) {
        navigate(from, { replace: true });
      } else {
        setError(result.error || 'Login failed. Please try again.');
      }
    } catch {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4 relative overflow-hidden">
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

      {/* 3D Particle Effects Background */}
      <div className="absolute inset-0 z-0">
        <Particles
          particleColors={['#2563eb', '#7c3aed', '#0891b2', '#dc2626', '#ea580c', '#16a34a']}
          particleCount={300}
          particleSpread={10}
          speed={0.2}
          particleBaseSize={120}
          moveParticlesOnHover={true}
          alphaParticles={false}
          disableRotation={false}
          particleHoverFactor={0.8}
          sizeRandomness={1.2}
          cameraDistance={22}
        />
      </div>

      <div className="absolute top-10 left-10 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
      <div className="absolute top-10 right-10 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
      <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>

      {/* Enhanced Glass Demo Badge */}
      <div className="fixed top-8 right-8 bg-gradient-to-r from-blue-600/80 to-purple-600/80 backdrop-blur-xl text-white px-6 py-3 rounded-full shadow-lg border border-white/30 animate-bounce-slow z-50 hover:from-blue-600/90 hover:to-purple-600/90 transition-all duration-300">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-semibold">🚀 Demo: <EMAIL> / demo123</span>
        </div>
      </div>

      <div className="relative w-full max-w-4xl flex items-center justify-center">
        {/* Ultra Glass Morphism Card Container */}
        <div className="bg-white/5 backdrop-blur-3xl rounded-3xl shadow-2xl border border-white/20 p-8 hover:shadow-3xl hover:bg-white/10 transition-all duration-500 relative w-full max-w-4xl before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/10 before:via-white/5 before:to-transparent before:rounded-3xl before:pointer-events-none after:absolute after:inset-0 after:bg-gradient-to-t after:from-transparent after:via-white/5 after:to-white/10 after:rounded-3xl after:pointer-events-none z-10">
          <div className="flex items-center justify-between">
            {/* Login Form - Left Side */}
            <div className="w-full max-w-sm">
            {/* Back Arrow - Top Left */}
            <Link
              to={ROUTES.LANDING}
              className="absolute top-4 left-4 text-gray-600 hover:text-blue-600 transition-all duration-300 hover:scale-110 group"
            >
              <ArrowLeft className="w-5 h-5 group-hover:scale-110 transition-transform" />
            </Link>
            {/* Header */}
            <div className="text-center mb-6 mt-8">
              <Link to={ROUTES.LANDING} className="inline-flex items-center justify-center mb-4 group">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-600 rounded-xl flex items-center justify-center mr-3 shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                  <span className="text-white font-bold text-xl">H</span>
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">HustleGPT</span>
              </Link>

              <h2 className="text-2xl font-bold text-gray-900 mb-2 tracking-tight">
                Welcome back
              </h2>
              <p className="text-gray-600 text-sm">
                Sign in to continue your hustle journey
              </p>
            </div>

            {/* Login Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {error && (
              <ErrorMessage
                title="Login Failed"
                message={error}
                variant="error"
                dismissible
                onDismiss={() => setError('')}
              />
            )}

            <div className="space-y-1">
              <label className="block text-xs font-medium text-gray-700 mb-1">Email address</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="w-4 h-4 text-gray-400" />
                </div>
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="w-full pl-10 pr-4 py-3 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-400/30 focus:border-white/40 focus:bg-white/20 transition-all duration-300 text-sm text-gray-900 placeholder-gray-700 hover:bg-white/15 shadow-inner"
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: 'Please enter a valid email address'
                    }
                  })}
                />
              </div>
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
            </div>

            <div className="space-y-1">
              <label className="block text-xs font-medium text-gray-700 mb-1">Password</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="w-4 h-4 text-gray-400" />
                </div>
                <input
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Enter your password"
                  className="w-full pl-10 pr-10 py-3 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-400/30 focus:border-white/40 focus:bg-white/20 transition-all duration-300 text-sm text-gray-900 placeholder-gray-700 hover:bg-white/15 shadow-inner"
                  {...register('password', {
                    required: 'Password is required',
                    minLength: {
                      value: 6,
                      message: 'Password must be at least 6 characters'
                    }
                  })}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              </div>
              {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-xs font-medium text-gray-700">
                  Remember me
                </label>
              </div>

              <Link
                to="/forgot-password"
                className="text-xs font-medium text-blue-600 hover:text-blue-800 transition-colors hover:underline"
              >
                Forgot password?
              </Link>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-blue-600/70 to-purple-600/70 backdrop-blur-xl text-white py-3 px-4 rounded-xl font-medium text-sm shadow-lg hover:shadow-xl hover:from-blue-600/80 hover:to-purple-600/80 transform hover:scale-[1.02] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none border border-white/30 relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/10 before:to-transparent before:pointer-events-none"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Signing in...
                </div>
              ) : (
                'Sign in'
              )}
            </button>
          </form>

              {/* Sign up link */}
              <div className="mt-6 text-center">
                <p className="text-gray-600 text-sm">
                  Don't have an account?{' '}
                  <Link
                    to={ROUTES.SIGNUP}
                    className="text-blue-600 hover:text-purple-600 font-medium transition-all duration-300 hover:underline"
                  >
                    Sign up for free
                  </Link>
                </p>
              </div>
            </div>

            {/* Right Side - Inspirational Content */}
            <div className="hidden lg:flex w-full max-w-sm ml-8 flex-col items-center justify-center">
              <div className="text-center space-y-4">
                {/* Animated Icon */}
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center shadow-lg animate-pulse-slow">
                    <span className="text-white text-3xl">💼</span>
                  </div>
                  <div className="absolute -top-2 -right-2 w-5 h-5 bg-yellow-400 rounded-full animate-bounce"></div>
                </div>

                {/* Inspirational Text */}
                <div className="space-y-3">
                  <h3 className="text-2xl font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Start Your Journey
                  </h3>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    Join entrepreneurs discovering opportunities with AI recommendations
                  </p>

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-3 mt-4">
                    <div className="text-center p-3 bg-white/15 backdrop-blur-xl rounded-xl border border-white/25 hover:bg-white/25 transition-all duration-300 shadow-inner relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/10 before:to-transparent before:pointer-events-none">
                      <div className="text-lg font-medium text-blue-600 relative z-10">10K+</div>
                      <div className="text-xs text-gray-800 relative z-10">Users</div>
                    </div>
                    <div className="text-center p-3 bg-white/15 backdrop-blur-xl rounded-xl border border-white/25 hover:bg-white/25 transition-all duration-300 shadow-inner relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/10 before:to-transparent before:pointer-events-none">
                      <div className="text-lg font-medium text-purple-600 relative z-10">500+</div>
                      <div className="text-xs text-gray-800 relative z-10">Stories</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
