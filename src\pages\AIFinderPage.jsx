import { useState, useEffect } from 'react';
import { ChevronLeft, CheckCircle, ArrowRight, Target, Star, Clock, DollarSign, Heart } from 'lucide-react';
import { <PERSON><PERSON>, Card, Badge } from '../components/ui';
import { aiFinderAPI } from '../services/api';

const AIFinderPage = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [recommendations, setRecommendations] = useState([]);
  const [savedHustles, setSavedHustles] = useState([]);
  const [selectedHustle, setSelectedHustle] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [formData, setFormData] = useState({
    budget: '',
    interests: [],
    skills: [],
    experience: '',
    timeCommitment: '',
    goals: [],
    workStyle: ''
  });
  const [filters, setFilters] = useState({
    difficulty: 'all',
    category: 'all',
    timeCommitment: 'all'
  });

  const totalSteps = 6;
  const categories = ['Writing', 'Technology', 'Marketing', 'Education', 'E-commerce', 'Design', 'Consulting', 'Creative'];

  // Question data
  const budgetOptions = [
    { value: 'no-investment', label: '$0 (No upfront investment)', icon: '💡' },
    { value: 'low-budget', label: '$100 - $500', icon: '$' },
    { value: 'medium-budget', label: '$500 - $5,000', icon: '$$' },
    { value: 'high-budget', label: '$5,000+', icon: '$$$' }
  ];

  const interestOptions = [
    'Freelancing', 'Online Business', 'Content Creation', 'Tutoring', 
    'E-commerce', 'Consulting', 'Digital Marketing', 'Creative Work',
    'Technology', 'Writing', 'Design', 'Photography'
  ];

  const skillOptions = [
    'Writing', 'Programming', 'Design', 'Marketing', 'Teaching', 
    'Sales', 'Photography', 'Video Editing', 'Social Media', 
    'Customer Service', 'Data Analysis', 'Project Management'
  ];

  const experienceOptions = [
    { value: 'complete-beginner', label: 'Complete Beginner', description: 'New to side hustles' },
    { value: 'some-experience', label: 'Some Experience', description: 'Tried a few things before' },
    { value: 'experienced', label: 'Experienced', description: 'Have successful side hustles' }
  ];

  const timeCommitmentOptions = [
    { value: 'part-time', label: '5-10 hours/week', description: 'Just getting started' },
    { value: 'moderate', label: '10-20 hours/week', description: 'Serious about growth' },
    { value: 'full-time', label: '20+ hours/week', description: 'Ready to scale big' }
  ];

  const goalOptions = [
    'Extra Income', 'Replace Full-time Job', 'Build Passive Income', 
    'Learn New Skills', 'Creative Expression', 'Financial Freedom'
  ];

  const workStyleOptions = [
    { value: 'flexible', label: 'Flexible Schedule', description: 'Work when I want' },
    { value: 'structured', label: 'Structured Routine', description: 'Set hours and deadlines' },
    { value: 'project-based', label: 'Project-Based', description: 'Complete projects as they come' }
  ];

  useEffect(() => {
    loadSavedHustles();
  }, []);

  const loadSavedHustles = () => {
    const saved = JSON.parse(localStorage.getItem('savedHustles') || '[]');
    setSavedHustles(saved);
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleFinalSubmit();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFormDataUpdate = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFinalSubmit = async () => {
    setIsLoading(true);
    try {
      // Validate form data before sending to AI
      if (!formData.budget || formData.interests.length === 0 || formData.skills.length === 0 ||
          !formData.experience || !formData.timeCommitment || formData.goals.length === 0 || !formData.workStyle) {
        throw new Error('Please complete all questionnaire steps before getting recommendations.');
      }

      // Create comprehensive user profile for AI analysis
      const userProfile = {
        budget: formData.budget,
        interests: formData.interests.length > 0 ? formData.interests : ['General'],
        skills: formData.skills.length > 0 ? formData.skills : ['General'],
        experience: formData.experience,
        timeCommitment: formData.timeCommitment,
        goals: formData.goals.length > 0 ? formData.goals : ['Extra Income'],
        workStyle: formData.workStyle,
        // Additional context for AI
        questionnaire: {
          step1_budget: formData.budget,
          step2_interests: formData.interests,
          step3_skills: formData.skills,
          step4_experience: formData.experience,
          step5_timeCommitment: formData.timeCommitment,
          step6_goals: formData.goals,
          step6_workStyle: formData.workStyle
        }
      };

      // Call AI API with complete user profile - AI ONLY MODE
      const results = await aiFinderAPI.getAIRecommendations(userProfile);

      // Validate AI results
      if (!results || !Array.isArray(results) || results.length === 0) {
        throw new Error('AI returned empty results. Please try again.');
      }

      setRecommendations(results);
      setCurrentStep(totalSteps + 1); // Move to results step
    } catch (error) {
      console.error('Error getting AI recommendations:', error);

      // Show user-friendly error message
      if (error.message.includes('complete all questionnaire')) {
        alert(error.message);
        return;
      }

      // AI-ONLY MODE: No fallback, show clear error message
      if (error.name === 'AbortError' || error.message.includes('timed out')) {
        alert('AI is taking longer than expected to analyze your profile. Please try again - sometimes the free AI model has high demand.');
      } else if (error.message.includes('API error')) {
        alert('AI service is temporarily unavailable. Please try again in a few moments.');
      } else {
        alert('Unable to get AI recommendations right now. Please check your internet connection and try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1: return formData.budget !== '';
      case 2: return formData.interests.length > 0;
      case 3: return formData.skills.length > 0;
      case 4: return formData.experience !== '';
      case 5: return formData.timeCommitment !== '';
      case 6: return formData.goals.length > 0 && formData.workStyle !== '';
      default: return true;
    }
  };

  const saveHustle = (hustle) => {
    const saved = [...savedHustles, hustle.id];
    setSavedHustles(saved);
    localStorage.setItem('savedHustles', JSON.stringify(saved));
  };

  const unsaveHustle = (hustleId) => {
    const saved = savedHustles.filter(id => id !== hustleId);
    setSavedHustles(saved);
    localStorage.setItem('savedHustles', JSON.stringify(saved));
  };

  const handleLearnMore = (hustle) => {
    setSelectedHustle(hustle);
    setShowDetailModal(true);
  };

  const closeDetailModal = () => {
    setShowDetailModal(false);
    setSelectedHustle(null);
  };

  const filteredRecommendations = recommendations.filter(hustle => {
    if (filters.difficulty !== 'all' && hustle.difficulty.toLowerCase() !== filters.difficulty) return false;
    if (filters.category !== 'all' && hustle.category !== filters.category) return false;
    return true;
  });

  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'beginner': return 'text-green-600 bg-green-100';
      case 'intermediate': return 'text-yellow-600 bg-yellow-100';
      case 'advanced': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Progress Indicator Component
  const ProgressIndicator = () => (
    <div className="mb-6 sm:mb-8">
      <div className="flex items-center justify-between mb-3 sm:mb-4">
        <span className="text-xs sm:text-sm font-medium text-gray-600">
          Step {currentStep} of {totalSteps}
        </span>
        <span className="text-xs sm:text-sm text-gray-500">
          {Math.round((currentStep / totalSteps) * 100)}% Complete
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2 sm:h-2.5">
        <div
          className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 sm:h-2.5 rounded-full transition-all duration-500 ease-out shadow-sm"
          style={{ width: `${(currentStep / totalSteps) * 100}%` }}
        />
      </div>
    </div>
  );

  // Step Navigation Component
  const StepNavigation = () => (
    <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-0 mt-6 sm:mt-8">
      <Button
        variant="outline"
        onClick={handlePrevious}
        disabled={currentStep === 1}
        leftIcon={<ChevronLeft className="w-4 h-4" />}
        className="order-2 sm:order-1"
        size="lg"
      >
        <span className="hidden sm:inline">Previous</span>
        <span className="sm:hidden">Back</span>
      </Button>
      <Button
        onClick={handleNext}
        disabled={!isStepValid()}
        loading={isLoading && currentStep === totalSteps}
        rightIcon={currentStep === totalSteps ? <CheckCircle className="w-4 h-4" /> : <ArrowRight className="w-4 h-4" />}
        className="order-1 sm:order-2"
        size="lg"
      >
        {currentStep === totalSteps ? (
          isLoading ? (
            <>
              <span className="hidden sm:inline">AI Analyzing Your Profile...</span>
              <span className="sm:hidden">AI Processing...</span>
            </>
          ) : (
            <>
              <span className="hidden sm:inline">Get AI Recommendations</span>
              <span className="sm:hidden">Get AI Results</span>
            </>
          )
        ) : (
          'Next'
        )}
      </Button>
    </div>
  );

  // Render individual step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4 sm:space-y-6">
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-4">What's Your Budget?</h2>
              <p className="text-sm sm:text-base text-gray-600 px-2">How much are you willing to invest upfront?</p>
            </div>
            <div className="space-y-3 sm:space-y-4">
              {budgetOptions.map((option) => (
                <label
                  key={option.value}
                  className={`flex items-center p-3 sm:p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-sm ${
                    formData.budget === option.value
                      ? 'border-blue-500 bg-blue-50 shadow-sm'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="budget"
                    value={option.value}
                    checked={formData.budget === option.value}
                    onChange={(e) => handleFormDataUpdate('budget', e.target.value)}
                    className="sr-only"
                  />
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center">
                      <span className="text-xl sm:text-2xl mr-3 sm:mr-4">{option.icon}</span>
                      <span className="font-medium text-sm sm:text-base text-gray-900">{option.label}</span>
                    </div>
                    {formData.budget === option.value && (
                      <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0" />
                    )}
                  </div>
                </label>
              ))}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4 sm:space-y-6">
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-4">What Are Your Interests?</h2>
              <p className="text-sm sm:text-base text-gray-600 px-2">Select all that apply to you</p>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3">
              {interestOptions.map((interest) => (
                <label
                  key={interest}
                  className={`flex items-center p-3 sm:p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-sm ${
                    formData.interests.includes(interest)
                      ? 'border-blue-500 bg-blue-50 shadow-sm'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="checkbox"
                    checked={formData.interests.includes(interest)}
                    onChange={(e) => {
                      const newInterests = e.target.checked
                        ? [...formData.interests, interest]
                        : formData.interests.filter(i => i !== interest);
                      handleFormDataUpdate('interests', newInterests);
                    }}
                    className="sr-only"
                  />
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm sm:text-base font-medium text-gray-900">{interest}</span>
                    {formData.interests.includes(interest) && (
                      <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0" />
                    )}
                  </div>
                </label>
              ))}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-4 sm:space-y-6">
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-4">What Skills Do You Have?</h2>
              <p className="text-sm sm:text-base text-gray-600 px-2">Select your existing skills</p>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3">
              {skillOptions.map((skill) => (
                <label
                  key={skill}
                  className={`flex items-center p-3 sm:p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-sm ${
                    formData.skills.includes(skill)
                      ? 'border-blue-500 bg-blue-50 shadow-sm'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="checkbox"
                    checked={formData.skills.includes(skill)}
                    onChange={(e) => {
                      const newSkills = e.target.checked
                        ? [...formData.skills, skill]
                        : formData.skills.filter(s => s !== skill);
                      handleFormDataUpdate('skills', newSkills);
                    }}
                    className="sr-only"
                  />
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm sm:text-base font-medium text-gray-900">{skill}</span>
                    {formData.skills.includes(skill) && (
                      <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0" />
                    )}
                  </div>
                </label>
              ))}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-4 sm:space-y-6">
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-4">What's Your Experience Level?</h2>
              <p className="text-sm sm:text-base text-gray-600 px-2">Choose the option that best describes you</p>
            </div>
            <div className="space-y-3 sm:space-y-4">
              {experienceOptions.map((option) => (
                <label
                  key={option.value}
                  className={`flex items-center p-3 sm:p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-sm ${
                    formData.experience === option.value
                      ? 'border-blue-500 bg-blue-50 shadow-sm'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="experience"
                    value={option.value}
                    checked={formData.experience === option.value}
                    onChange={(e) => handleFormDataUpdate('experience', e.target.value)}
                    className="sr-only"
                  />
                  <div className="flex items-center justify-between w-full">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm sm:text-base text-gray-900">{option.label}</div>
                      <div className="text-xs sm:text-sm text-gray-600 mt-1">{option.description}</div>
                    </div>
                    {formData.experience === option.value && (
                      <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0 ml-3" />
                    )}
                  </div>
                </label>
              ))}
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-4 sm:space-y-6">
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-4">How Much Time Can You Commit?</h2>
              <p className="text-sm sm:text-base text-gray-600 px-2">Choose your preferred time commitment</p>
            </div>
            <div className="space-y-3 sm:space-y-4">
              {timeCommitmentOptions.map((option) => (
                <label
                  key={option.value}
                  className={`flex items-center p-3 sm:p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-sm ${
                    formData.timeCommitment === option.value
                      ? 'border-blue-500 bg-blue-50 shadow-sm'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="timeCommitment"
                    value={option.value}
                    checked={formData.timeCommitment === option.value}
                    onChange={(e) => handleFormDataUpdate('timeCommitment', e.target.value)}
                    className="sr-only"
                  />
                  <div className="flex items-center justify-between w-full">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm sm:text-base text-gray-900">{option.label}</div>
                      <div className="text-xs sm:text-sm text-gray-600 mt-1">{option.description}</div>
                    </div>
                    {formData.timeCommitment === option.value && (
                      <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0 ml-3" />
                    )}
                  </div>
                </label>
              ))}
            </div>
          </div>
        );

      case 6:
        return (
          <div className="space-y-4 sm:space-y-6">
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-4">Final Questions</h2>
              <p className="text-sm sm:text-base text-gray-600 px-2">Tell us about your goals and work style</p>
            </div>

            <div className="space-y-6 sm:space-y-8">
              {/* Goals Section */}
              <div>
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">What are your goals?</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3">
                  {goalOptions.map((goal) => (
                    <label
                      key={goal}
                      className={`flex items-center p-3 sm:p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-sm ${
                        formData.goals.includes(goal)
                          ? 'border-blue-500 bg-blue-50 shadow-sm'
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={formData.goals.includes(goal)}
                        onChange={(e) => {
                          const newGoals = e.target.checked
                            ? [...formData.goals, goal]
                            : formData.goals.filter(g => g !== goal);
                          handleFormDataUpdate('goals', newGoals);
                        }}
                        className="sr-only"
                      />
                      <div className="flex items-center justify-between w-full">
                        <span className="text-sm sm:text-base font-medium text-gray-900">{goal}</span>
                        {formData.goals.includes(goal) && (
                          <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0" />
                        )}
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Work Style Section */}
              <div>
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">What's your preferred work style?</h3>
                <div className="space-y-3 sm:space-y-4">
                  {workStyleOptions.map((option) => (
                    <label
                      key={option.value}
                      className={`flex items-center p-3 sm:p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-sm ${
                        formData.workStyle === option.value
                          ? 'border-blue-500 bg-blue-50 shadow-sm'
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      <input
                        type="radio"
                        name="workStyle"
                        value={option.value}
                        checked={formData.workStyle === option.value}
                        onChange={(e) => handleFormDataUpdate('workStyle', e.target.value)}
                        className="sr-only"
                      />
                      <div className="flex items-center justify-between w-full">
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm sm:text-base text-gray-900">{option.label}</div>
                          <div className="text-xs sm:text-sm text-gray-600 mt-1">{option.description}</div>
                        </div>
                        {formData.workStyle === option.value && (
                          <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0 ml-3" />
                        )}
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (currentStep <= totalSteps) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 sm:py-12">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="text-center mb-8 sm:mb-12">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">
              AI Hustle Finder
            </h1>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-xl mx-auto px-4">
              Answer a few questions and let our AI find the perfect side hustles for you
            </p>
          </div>

          {/* Main Card */}
          <Card className="p-4 sm:p-6 lg:p-8 shadow-xl border-0">
            <ProgressIndicator />
            {renderStepContent()}
            <StepNavigation />
          </Card>
        </div>
      </div>
    );
  }

  // Results page
  return (
    <div className="min-h-screen bg-gray-50 py-6 sm:py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 sm:mb-8">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Your AI Recommendations</h1>
            <p className="text-sm sm:text-base text-gray-600 mt-1 sm:mt-2">
              Found {filteredRecommendations.length} perfect matches for you
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => setCurrentStep(1)}
            leftIcon={<ArrowRight className="w-4 h-4 rotate-180" />}
            size="sm"
            className="self-center sm:self-auto"
          >
            <span className="hidden sm:inline">Retake Quiz</span>
            <span className="sm:hidden">Retake</span>
          </Button>
        </div>

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
          <div className="flex flex-wrap gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty</label>
              <select
                value={filters.difficulty}
                onChange={(e) => setFilters({...filters, difficulty: e.target.value})}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Levels</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <select
                value={filters.category}
                onChange={(e) => setFilters({...filters, category: e.target.value})}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Categories</option>
                {categories.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* AI Analysis Summary - AI ONLY MODE */}
        {filteredRecommendations.length > 0 && (
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6 mb-6">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                <Target className="w-4 h-4 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">🤖 AI Analysis Complete</h3>
            </div>
            <p className="text-gray-700 text-sm">
              <strong>Nvidia Llama AI</strong> analyzed your complete profile across {totalSteps} questionnaire steps and generated {filteredRecommendations.length} highly personalized recommendations.
              Each recommendation includes AI confidence scores and specific reasons why it perfectly matches your unique profile.
            </p>
          </div>
        )}

        {/* Recommendations Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRecommendations.map((hustle) => (
            <Card key={hustle.id} className="p-6 hover:shadow-lg transition-shadow flex flex-col h-full">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{hustle.title}</h3>
                    {hustle.aiGenerated && hustle.confidence && (
                      <Badge className="text-xs px-2 py-1 bg-green-100 text-green-800">
                        {hustle.confidence}% match
                      </Badge>
                    )}
                  </div>
                  <Badge className={`text-xs px-2 py-1 rounded-full ${getDifficultyColor(hustle.difficulty)}`}>
                    {hustle.difficulty}
                  </Badge>
                </div>
                <button
                  onClick={() => savedHustles.includes(hustle.id) ? unsaveHustle(hustle.id) : saveHustle(hustle)}
                  className="text-gray-400 hover:text-red-500 transition-colors flex-shrink-0"
                >
                  <Heart className={`w-5 h-5 ${savedHustles.includes(hustle.id) ? 'fill-current text-red-500' : ''}`} />
                </button>
              </div>

              <p className="text-gray-600 text-sm mb-4">{hustle.description}</p>

              {/* AI Match Reasons */}
              {hustle.matchReasons && hustle.matchReasons.length > 0 && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                  <h4 className="text-xs font-semibold text-blue-800 mb-2">Why this matches you:</h4>
                  <ul className="text-xs text-blue-700 space-y-1">
                    {hustle.matchReasons.slice(0, 3).map((reason, index) => (
                      <li key={index} className="flex items-start">
                        <span className="w-1 h-1 bg-blue-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                        {reason}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="w-4 h-4 mr-2" />
                  {hustle.timeCommitment}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <DollarSign className="w-4 h-4 mr-2" />
                  {hustle.potentialEarnings}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Star className="w-4 h-4 mr-2" />
                  {hustle.category}
                </div>
              </div>

              {hustle.trending && (
                <div className="flex items-center mb-4">
                  <Star className="w-4 h-4 text-yellow-500 mr-1" />
                  <span className="text-sm text-yellow-600 font-medium">Trending</span>
                </div>
              )}

              <div className="flex flex-wrap gap-1 mb-4">
                {hustle.tags && hustle.tags.slice(0, 3).map((tag, index) => (
                  <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    {tag}
                  </span>
                ))}
              </div>

              {/* Push button to bottom */}
              <div className="mt-auto">
                <Button
                  size="sm"
                  fullWidth
                  onClick={() => handleLearnMore(hustle)}
                >
                  Learn More
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {filteredRecommendations.length === 0 && (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
              <Target className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No matches found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your filters or retaking the quiz</p>
            <Button onClick={() => setCurrentStep(1)}>Retake Quiz</Button>
          </div>
        )}

        {/* Detail Modal */}
        {showDetailModal && selectedHustle && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                {/* Modal Header */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h2 className="text-2xl font-bold text-gray-900">{selectedHustle.title}</h2>
                      {selectedHustle.aiGenerated && selectedHustle.confidence && (
                        <Badge className="text-sm px-3 py-1 bg-green-100 text-green-800">
                          {selectedHustle.confidence}% AI Match
                        </Badge>
                      )}
                    </div>
                    <Badge className={`text-sm px-3 py-1 rounded-full ${getDifficultyColor(selectedHustle.difficulty)}`}>
                      {selectedHustle.difficulty}
                    </Badge>
                  </div>
                  <button
                    onClick={closeDetailModal}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {/* Description */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">About This Opportunity</h3>
                  <p className="text-gray-700 leading-relaxed">{selectedHustle.description}</p>
                </div>

                {/* AI Match Reasons */}
                {selectedHustle.matchReasons && selectedHustle.matchReasons.length > 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 className="text-lg font-semibold text-blue-900 mb-3">🤖 Why AI Chose This For You</h4>
                    <ul className="space-y-2">
                      {selectedHustle.matchReasons.map((reason, index) => (
                        <li key={index} className="flex items-start text-blue-800">
                          <span className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          {reason}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Key Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div className="space-y-4">
                    <div className="flex items-center text-gray-700">
                      <Clock className="w-5 h-5 mr-3 text-gray-500" />
                      <div>
                        <div className="font-medium">Time Commitment</div>
                        <div className="text-sm text-gray-600">{selectedHustle.timeCommitment}</div>
                      </div>
                    </div>
                    <div className="flex items-center text-gray-700">
                      <DollarSign className="w-5 h-5 mr-3 text-gray-500" />
                      <div>
                        <div className="font-medium">Potential Earnings</div>
                        <div className="text-sm text-gray-600">{selectedHustle.potentialEarnings}</div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center text-gray-700">
                      <Target className="w-5 h-5 mr-3 text-gray-500" />
                      <div>
                        <div className="font-medium">Category</div>
                        <div className="text-sm text-gray-600">{selectedHustle.category}</div>
                      </div>
                    </div>
                    {selectedHustle.trending && (
                      <div className="flex items-center text-gray-700">
                        <Star className="w-5 h-5 mr-3 text-yellow-500" />
                        <div>
                          <div className="font-medium text-yellow-600">Trending Opportunity</div>
                          <div className="text-sm text-gray-600">High demand right now</div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Requirements */}
                {selectedHustle.requirements && selectedHustle.requirements.length > 0 && (
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">Requirements</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedHustle.requirements.map((req, index) => (
                        <span key={index} className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                          {req}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Tags */}
                {selectedHustle.tags && selectedHustle.tags.length > 0 && (
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">Related Topics</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedHustle.tags.map((tag, index) => (
                        <span key={index} className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4 border-t">
                  <Button
                    onClick={() => savedHustles.includes(selectedHustle.id) ? unsaveHustle(selectedHustle.id) : saveHustle(selectedHustle)}
                    variant={savedHustles.includes(selectedHustle.id) ? "outline" : "primary"}
                    className="flex-1"
                  >
                    <Heart className={`w-4 h-4 mr-2 ${savedHustles.includes(selectedHustle.id) ? 'fill-current' : ''}`} />
                    {savedHustles.includes(selectedHustle.id) ? 'Saved' : 'Save This Hustle'}
                  </Button>
                  <Button onClick={closeDetailModal} variant="outline" className="flex-1">
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIFinderPage;
