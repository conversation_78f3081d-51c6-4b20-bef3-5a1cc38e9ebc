# Netlify Redirects for React SPA
# This ensures all routes are handled by React Router
# CRITICAL: This file is a backup to netlify.toml for SPA routing

# Primary SPA fallback - redirect all routes to index.html
# This fixes "page not found" errors when users refresh or access direct URLs
/*    /index.html   200

# Security redirects - block access to sensitive files
/.env*    /    301
/config/*    /    301
/.git/*    /    301
/node_modules/*    /    301

# Legacy route redirects (if you had old URLs)
/old-path/*    /new-path/:splat    301

# Asset optimization redirects (optional)
/images/*    /assets/images/:splat    200
