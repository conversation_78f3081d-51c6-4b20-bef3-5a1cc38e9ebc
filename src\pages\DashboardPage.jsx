import { useState, useEffect } from 'react';
import { Link, Navigate } from 'react-router-dom';
import {
  User,
  Target,
  DollarSign,
  TrendingUp,
  Clock,
  Star,
  ArrowRight,
  Settings,
  Edit,
  Calendar,
  Award,
  BookOpen,
  Heart,
  Eye
} from 'lucide-react';
import { <PERSON><PERSON>, Card, Badge, Modal, Input } from '../components/ui';
import { useAuth } from '../context/AuthContext';
import { ROUTES } from '../constants/routes';

const DashboardPage = () => {
  const { isAuthenticated, user, updateProfile } = useAuth();
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [savedHustles, setSavedHustles] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [goals, setGoals] = useState([]);

  // Redirect non-authenticated users
  if (!isAuthenticated) {
    return <Navigate to={ROUTES.LOGIN} replace />;
  }

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = () => {
    // Load saved hustles from localStorage
    const saved = JSON.parse(localStorage.getItem('savedHustles') || '[]');
    setSavedHustles(saved);

    // Mock recent activity
    setRecentActivity([
      { action: 'Completed', item: 'AI Finder Quiz', time: '2 hours ago', type: 'quiz' },
      { action: 'Saved', item: 'Freelance Writing', time: '1 day ago', type: 'save' },
      { action: 'Viewed', item: 'Success Story: Sarah\'s Journey', time: '2 days ago', type: 'view' },
      { action: 'Started', item: 'Online Tutoring Guide', time: '3 days ago', type: 'read' },
      { action: 'Joined', item: 'HustleGPT Community', time: '1 week ago', type: 'join' }
    ]);

    // Mock goals
    setGoals([
      { id: 1, title: 'Complete Profile', progress: 75, target: 100, completed: false },
      { id: 2, title: 'Try First Hustle', progress: 0, target: 1, completed: false },
      { id: 3, title: 'Earn First $100', progress: 0, target: 100, completed: false },
      { id: 4, title: 'Share Success Story', progress: 0, target: 1, completed: false }
    ]);
  };

  const stats = [
    {
      icon: Target,
      label: 'Hustles Tried',
      value: user?.hustlesTried || 0,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      icon: DollarSign,
      label: 'Monthly Earnings',
      value: '$0',
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      icon: Star,
      label: 'Success Stories',
      value: user?.successStories || 0,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      icon: Clock,
      label: 'Days Active',
      value: user?.joinedDate ? Math.floor((new Date() - new Date(user.joinedDate)) / (1000 * 60 * 60 * 24)) : 0,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  const quickActions = [
    {
      title: 'Find New Hustles',
      description: 'Get AI-powered recommendations',
      icon: Target,
      link: ROUTES.AI_FINDER,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Read Success Stories',
      description: 'Get inspired by others',
      icon: Award,
      link: ROUTES.SUCCESS_STORIES,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Browse Blog',
      description: 'Learn tips and strategies',
      icon: BookOpen,
      link: ROUTES.BLOG,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Check Trending',
      description: 'See what\'s popular now',
      icon: TrendingUp,
      link: ROUTES.TRENDING,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  const getActivityIcon = (type) => {
    switch (type) {
      case 'quiz': return <Target className="w-4 h-4 text-blue-500" />;
      case 'save': return <Heart className="w-4 h-4 text-red-500" />;
      case 'view': return <Eye className="w-4 h-4 text-gray-500" />;
      case 'read': return <BookOpen className="w-4 h-4 text-green-500" />;
      case 'join': return <User className="w-4 h-4 text-purple-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome back, {user?.name?.split(' ')[0] || 'Entrepreneur'}! 👋
            </h1>
            <p className="text-gray-600 mt-2">
              Here's what's happening with your hustle journey
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => setShowEditProfile(true)}
            leftIcon={<Settings className="w-4 h-4" />}
          >
            Settings
          </Button>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index}>
                <Card.Content className="p-6">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                  </div>
                </Card.Content>
              </Card>
            );
          })}
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Quick Actions */}
            <Card>
              <Card.Header>
                <Card.Title>Quick Actions</Card.Title>
                <Card.Description>Jump into your hustle journey</Card.Description>
              </Card.Header>
              <Card.Content>
                <div className="grid md:grid-cols-2 gap-4">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <Link key={index} to={action.link}>
                        <div className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
                          <div className="flex items-center mb-3">
                            <div className={`p-2 rounded-lg ${action.bgColor}`}>
                              <Icon className={`w-5 h-5 ${action.color}`} />
                            </div>
                            <h3 className="ml-3 font-semibold text-gray-900">{action.title}</h3>
                          </div>
                          <p className="text-sm text-gray-600">{action.description}</p>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              </Card.Content>
            </Card>

            {/* Goals & Progress */}
            <Card>
              <Card.Header>
                <Card.Title>Your Goals</Card.Title>
                <Card.Description>Track your progress towards success</Card.Description>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  {goals.map((goal) => (
                    <div key={goal.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{goal.title}</h4>
                        <div className="mt-2">
                          <div className="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>{goal.progress}/{goal.target}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${(goal.progress / goal.target) * 100}%` }}
                            />
                          </div>
                        </div>
                      </div>
                      {goal.completed && (
                        <div className="ml-4">
                          <Badge variant="success">Completed</Badge>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </Card.Content>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Profile Card */}
            <Card>
              <Card.Content className="p-6 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                  {user?.name?.charAt(0) || 'U'}
                </div>
                <h3 className="text-lg font-semibold text-gray-900">{user?.name || 'User'}</h3>
                <p className="text-gray-600 text-sm mb-4">{user?.email}</p>
                <div className="text-sm text-gray-500 mb-4">
                  <div className="flex items-center justify-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    Joined {user?.joinedDate ? new Date(user.joinedDate).toLocaleDateString() : 'Recently'}
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowEditProfile(true)}
                  leftIcon={<Edit className="w-4 h-4" />}
                  fullWidth
                >
                  Edit Profile
                </Button>
              </Card.Content>
            </Card>

            {/* Recent Activity */}
            <Card>
              <Card.Header>
                <Card.Title>Recent Activity</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 mt-1">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="ml-3 flex-1">
                        <p className="text-sm text-gray-900">
                          <span className="font-medium">{activity.action}</span> {activity.item}
                        </p>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <Button variant="outline" size="sm" fullWidth>
                    View All Activity
                  </Button>
                </div>
              </Card.Content>
            </Card>

            {/* Saved Hustles */}
            <Card>
              <Card.Header>
                <Card.Title>Saved Hustles</Card.Title>
              </Card.Header>
              <Card.Content>
                {savedHustles.length > 0 ? (
                  <div className="space-y-3">
                    {savedHustles.slice(0, 3).map((hustleId, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <span className="text-sm text-gray-900">Hustle #{hustleId}</span>
                        <Button variant="ghost" size="sm">
                          <ArrowRight className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                    <Button variant="outline" size="sm" fullWidth>
                      View All Saved
                    </Button>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <Heart className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600 mb-3">No saved hustles yet</p>
                    <Link to={ROUTES.AI_FINDER}>
                      <Button size="sm">Find Hustles</Button>
                    </Link>
                  </div>
                )}
              </Card.Content>
            </Card>
          </div>
        </div>

        {/* Edit Profile Modal */}
        <EditProfileModal
          isOpen={showEditProfile}
          onClose={() => setShowEditProfile(false)}
          user={user}
          onUpdate={updateProfile}
        />
      </div>
    </div>
  );
};

// Edit Profile Modal Component
const EditProfileModal = ({ isOpen, onClose, user, onUpdate }) => {
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onUpdate(formData);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Edit Profile">
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          label="Full Name"
          value={formData.name}
          onChange={(e) => setFormData({...formData, name: e.target.value})}
          required
        />
        <Input
          label="Email Address"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({...formData, email: e.target.value})}
          required
        />

        <Modal.Footer>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">
            Save Changes
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default DashboardPage;
