import { createContext, useContext, useState, useEffect } from 'react';

// Create Auth Context
const AuthContext = createContext();

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Initialize auth state - CHECK FOR EXISTING AUTH
  useEffect(() => {
    const initializeAuth = () => {
      try {
        // Check for existing auth data
        const savedUser = localStorage.getItem('hustlegpt_user');
        const savedToken = localStorage.getItem('hustlegpt_token');

        if (savedUser && savedToken) {
          // User is already authenticated
          const userData = JSON.parse(savedUser);
          setUser(userData);
          setIsAuthenticated(true);
        } else {
          // No existing auth data
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Clear corrupted data
        localStorage.removeItem('hustlegpt_user');
        localStorage.removeItem('hustlegpt_token');
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Login function with proper validation
  const login = async (email, password) => {
    try {
      setIsLoading(true);

      // Validate input
      if (!email || !password) {
        return { success: false, error: 'Email and password are required' };
      }

      // Demo credentials for testing
      const validCredentials = [
        { email: '<EMAIL>', password: 'demo123', name: 'Demo User' },
        { email: '<EMAIL>', password: 'test123', name: 'Test User' },
        { email: '<EMAIL>', password: 'admin123', name: 'Admin User' }
      ];

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check credentials
      const validUser = validCredentials.find(
        cred => cred.email === email && cred.password === password
      );

      if (!validUser) {
        return { success: false, error: 'Invalid email or password' };
      }

      // Create user data
      const userData = {
        id: Date.now().toString(),
        name: validUser.name,
        email: validUser.email,
        avatar: null,
        joinedDate: new Date().toISOString(),
        hustlesTried: Math.floor(Math.random() * 10),
        successStories: Math.floor(Math.random() * 5)
      };

      const token = `jwt-token-${Date.now()}`;

      // Store in localStorage
      localStorage.setItem('hustlegpt_user', JSON.stringify(userData));
      localStorage.setItem('hustlegpt_token', token);

      setUser(userData);
      setIsAuthenticated(true);

      return { success: true, user: userData };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  // Signup function with proper validation
  const signup = async (name, email, password) => {
    try {
      setIsLoading(true);

      // Validate input
      if (!name || !email || !password) {
        return { success: false, error: 'All fields are required' };
      }

      if (password.length < 6) {
        return { success: false, error: 'Password must be at least 6 characters' };
      }

      // Check if email already exists (demo check)
      const existingEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
      if (existingEmails.includes(email)) {
        return { success: false, error: 'Email already exists. Please use a different email.' };
      }

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const userData = {
        id: Date.now().toString(),
        name: name,
        email: email,
        avatar: null,
        joinedDate: new Date().toISOString(),
        hustlesTried: 0,
        successStories: 0
      };

      const token = `jwt-token-${Date.now()}`;

      localStorage.setItem('hustlegpt_user', JSON.stringify(userData));
      localStorage.setItem('hustlegpt_token', token);

      setUser(userData);
      setIsAuthenticated(true);

      return { success: true, user: userData };
    } catch (error) {
      console.error('Signup error:', error);
      return { success: false, error: 'Signup failed. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('hustlegpt_user');
    localStorage.removeItem('hustlegpt_token');
    setUser(null);
    setIsAuthenticated(false);
  };

  // Update user profile
  const updateProfile = (updatedData) => {
    const updatedUser = { ...user, ...updatedData };
    localStorage.setItem('hustlegpt_user', JSON.stringify(updatedUser));
    setUser(updatedUser);
  };

  const value = {
    user,
    isAuthenticated,
    isLoading,
    login,
    signup,
    logout,
    updateProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
