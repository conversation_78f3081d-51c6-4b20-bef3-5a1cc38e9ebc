import React, { forwardRef } from 'react';
import { AlertCircle } from 'lucide-react';
import clsx from 'clsx';

const Textarea = forwardRef(({
  label,
  error,
  helperText,
  placeholder,
  disabled = false,
  required = false,
  fullWidth = true,
  rows = 4,
  resize = 'vertical',
  className,
  ...props
}, ref) => {
  const baseClasses = 'block w-full rounded-lg border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed';
  
  const variants = {
    default: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
    error: 'border-red-300 focus:border-red-500 focus:ring-red-500',
  };
  
  const resizeClasses = {
    none: 'resize-none',
    vertical: 'resize-y',
    horizontal: 'resize-x',
    both: 'resize',
  };
  
  const textareaClasses = clsx(
    baseClasses,
    variants[error ? 'error' : 'default'],
    resizeClasses[resize],
    'px-3 py-2 text-sm',
    className
  );
  
  return (
    <div className={clsx('relative', fullWidth && 'w-full')}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <textarea
        ref={ref}
        className={textareaClasses}
        placeholder={placeholder}
        disabled={disabled}
        rows={rows}
        {...props}
      />
      
      {(error || helperText) && (
        <div className="mt-1 flex items-center">
          {error && (
            <>
              <AlertCircle className="h-4 w-4 text-red-500 mr-1" />
              <span className="text-sm text-red-600">{error}</span>
            </>
          )}
          {!error && helperText && (
            <span className="text-sm text-gray-500">{helperText}</span>
          )}
        </div>
      )}
    </div>
  );
});

Textarea.displayName = 'Textarea';

export default Textarea;
