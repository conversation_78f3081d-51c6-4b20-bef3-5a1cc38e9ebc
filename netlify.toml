# Netlify Configuration for HustleGPT
# This file ensures proper deployment and routing for React SPA

[build]
  # Build command
  command = "npm run build"
  
  # Directory to publish (Vite builds to 'dist' by default)
  publish = "dist"
  
  # Environment variables for build
  [build.environment]
    NODE_VERSION = "18"
    NPM_VERSION = "9"
    CI = "true"

# CRITICAL: SPA Routing Configuration
# This is the most important part for fixing "page not found" issues
# When users refresh the page or access direct URLs, this redirects to index.html
# allowing React Router to handle the routing client-side
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false

# Security Headers for enhanced protection
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Performance headers
    Cache-Control = "public, max-age=********, immutable"

# Specific headers for HTML files (no caching for HTML)
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Headers for JavaScript and CSS files
[[headers]]
  for = "/assets/*.js"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/assets/*.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Build processing optimizations
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Form handling (if you add contact forms later)
[forms]
  settings = true

# Dev server settings for local development with Netlify CLI
[dev]
  command = "npm run dev"
  port = 5173
  publish = "dist"
  autoLaunch = false
