import { useState } from 'react';

const BlogImage = ({ 
  src, 
  alt, 
  category, 
  className = "", 
  fallbackGradient = "from-gray-400 to-gray-600",
  showHoverEffect = true 
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  return (
    <div className={`aspect-video bg-gray-200 rounded-t-lg overflow-hidden ${className}`}>
      {/* Actual Image */}
      {src && !imageError && (
        <img 
          src={src} 
          alt={alt}
          className={`w-full h-full object-cover transition-transform duration-300 ${
            showHoverEffect ? 'hover:scale-105' : ''
          } ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading="lazy"
        />
      )}
      
      {/* Loading State */}
      {src && !imageLoaded && !imageError && (
        <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
          <div className="text-gray-400 text-sm">Loading...</div>
        </div>
      )}
      
      {/* Fallback Gradient */}
      {(!src || imageError) && (
        <div 
          className={`w-full h-full bg-gradient-to-br ${fallbackGradient} flex items-center justify-center text-white font-semibold`}
        >
          {category || 'Blog Post'}
        </div>
      )}
    </div>
  );
};

export default BlogImage;
