import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import {
  ArrowLeft,
  DollarSign,
  Clock,
  Star,
  Heart,
  Share2,
  CheckCircle,
  Users,
  TrendingUp,
  BookOpen,
  Target,
  AlertCircle
} from 'lucide-react';
import { <PERSON>ton, Card, Badge } from '../components/ui';
import { LoadingSpinner } from '../components/common';
import { hustlesAPI } from '../services/api';
import { ROUTES } from '../constants/routes';

const HustleDetailPage = () => {
  const { id } = useParams();
  const [hustle, setHustle] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaved, setIsSaved] = useState(false);
  const [relatedHustles, setRelatedHustles] = useState([]);

  useEffect(() => {
    loadHustleDetails();
    checkIfSaved();
  }, [id]);

  const loadHustleDetails = async () => {
    setIsLoading(true);
    try {
      const hustleData = await hustlesAPI.getById(id);
      setHustle(hustleData);

      // Load related hustles
      const allHustles = await hustlesAPI.getAll();
      const related = allHustles
        .filter(h => h.id !== id && h.category === hustleData.category)
        .slice(0, 3);
      setRelatedHustles(related);
    } catch (error) {
      console.error('Error loading hustle details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const checkIfSaved = () => {
    const savedHustles = JSON.parse(localStorage.getItem('savedHustles') || '[]');
    setIsSaved(savedHustles.includes(id));
  };

  const toggleSave = () => {
    const savedHustles = JSON.parse(localStorage.getItem('savedHustles') || '[]');
    let newSaved;

    if (isSaved) {
      newSaved = savedHustles.filter(hustleId => hustleId !== id);
    } else {
      newSaved = [...savedHustles, id];
    }

    localStorage.setItem('savedHustles', JSON.stringify(newSaved));
    setIsSaved(!isSaved);
  };

  const shareHustle = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: hustle.title,
          text: hustle.description,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'beginner': return 'text-green-600 bg-green-100';
      case 'intermediate': return 'text-yellow-600 bg-yellow-100';
      case 'advanced': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (isLoading) {
    return <LoadingSpinner fullScreen text="Loading hustle details..." />;
  }

  if (!hustle) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Hustle not found</h2>
          <p className="text-gray-600 mb-6">The hustle you're looking for doesn't exist or has been removed.</p>
          <Link to={ROUTES.AI_FINDER}>
            <Button>Browse All Hustles</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <Link
          to={ROUTES.AI_FINDER}
          className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-8 transition-colors"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Hustles
        </Link>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Header */}
            <Card className="p-8 mb-8">
              <div className="flex items-start justify-between mb-6">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-4">
                    <Badge className={getDifficultyColor(hustle.difficulty)}>
                      {hustle.difficulty}
                    </Badge>
                    <Badge variant="outline">{hustle.category}</Badge>
                    {hustle.trending && (
                      <Badge variant="warning" className="flex items-center">
                        <TrendingUp className="w-3 h-3 mr-1" />
                        Trending
                      </Badge>
                    )}
                  </div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-4">{hustle.title}</h1>
                  <p className="text-lg text-gray-600 leading-relaxed">{hustle.description}</p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6">
                  <div className="flex items-center text-gray-600">
                    <DollarSign className="w-5 h-5 mr-2" />
                    <span className="font-medium">{hustle.potentialEarnings}</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Clock className="w-5 h-5 mr-2" />
                    <span className="font-medium">{hustle.timeCommitment}</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Star className="w-5 h-5 mr-2 text-yellow-400 fill-current" />
                    <span className="font-medium">{hustle.popularity || 85}% match</span>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    onClick={toggleSave}
                    leftIcon={<Heart className={`w-4 h-4 ${isSaved ? 'fill-current text-red-500' : ''}`} />}
                  >
                    {isSaved ? 'Saved' : 'Save'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={shareHustle}
                    leftIcon={<Share2 className="w-4 h-4" />}
                  >
                    Share
                  </Button>
                  <Button>Get Started</Button>
                </div>
              </div>
            </Card>

            {/* Requirements */}
            <Card className="p-8 mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Requirements</h2>
              <div className="grid md:grid-cols-2 gap-4">
                {hustle.requirements?.map((requirement, index) => (
                  <div key={index} className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{requirement}</span>
                  </div>
                ))}
              </div>
            </Card>

            {/* Getting Started Guide */}
            <Card className="p-8 mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">How to Get Started</h2>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">
                    1
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Build Your Skills</h3>
                    <p className="text-gray-600">Start by developing the core skills needed for this hustle. Take online courses, practice regularly, and build a portfolio.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">
                    2
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Create Your Profile</h3>
                    <p className="text-gray-600">Set up professional profiles on relevant platforms. Showcase your skills, experience, and previous work.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">
                    3
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Find Your First Client</h3>
                    <p className="text-gray-600">Start with smaller projects to build your reputation. Use networking, referrals, and online platforms to find opportunities.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">
                    4
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Scale Your Business</h3>
                    <p className="text-gray-600">Once you have steady clients, focus on scaling. Increase your rates, take on bigger projects, and consider hiring help.</p>
                  </div>
                </div>
              </div>
            </Card>

            {/* Tags */}
            <Card className="p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Tags</h2>
              <div className="flex flex-wrap gap-2">
                {hustle.tags?.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Quick Stats */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Popularity</span>
                  <span className="font-medium">{hustle.popularity || 85}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Difficulty</span>
                  <Badge className={getDifficultyColor(hustle.difficulty)} size="sm">
                    {hustle.difficulty}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Category</span>
                  <span className="font-medium">{hustle.category}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Views</span>
                  <span className="font-medium">12.5K</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Success Rate</span>
                  <span className="font-medium text-green-600">78%</span>
                </div>
              </div>
            </Card>

            {/* Related Hustles */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Hustles</h3>
              <div className="space-y-4">
                {relatedHustles.map((related) => (
                  <Link key={related.id} to={`/hustle/${related.id}`}>
                    <div className="p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-all cursor-pointer">
                      <h4 className="font-medium text-gray-900 mb-1">{related.title}</h4>
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">{related.description}</p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{related.category}</span>
                        <Badge className={getDifficultyColor(related.difficulty)} size="sm">
                          {related.difficulty}
                        </Badge>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
              <Link to={ROUTES.AI_FINDER}>
                <Button variant="outline" size="sm" fullWidth className="mt-4">
                  View All Hustles
                </Button>
              </Link>
            </Card>

            {/* Community */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Community</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <Users className="w-5 h-5 text-blue-600 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">2.3K people</div>
                    <div className="text-sm text-gray-600">are doing this hustle</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <BookOpen className="w-5 h-5 text-green-600 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">45 guides</div>
                    <div className="text-sm text-gray-600">available in our blog</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Target className="w-5 h-5 text-purple-600 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">156 success stories</div>
                    <div className="text-sm text-gray-600">shared by the community</div>
                  </div>
                </div>
              </div>
              <Link to={ROUTES.SUCCESS_STORIES}>
                <Button variant="outline" size="sm" fullWidth className="mt-4">
                  Read Success Stories
                </Button>
              </Link>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HustleDetailPage;
