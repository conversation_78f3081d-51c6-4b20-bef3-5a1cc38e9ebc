import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Loader2 } from 'lucide-react';

const RedirectPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  useEffect(() => {
    const redirectTo = searchParams.get('to') || '/';
    const delay = parseInt(searchParams.get('delay')) || 2000;
    
    const timer = setTimeout(() => {
      navigate(redirectTo, { replace: true });
    }, delay);
    
    return () => clearTimeout(timer);
  }, [navigate, searchParams]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
          <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
        </div>
        
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">
          Redirecting...
        </h1>
        
        <p className="text-gray-600">
          Please wait while we redirect you to your destination.
        </p>
      </div>
    </div>
  );
};

export default RedirectPage;
