import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // Build configuration for Netlify
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false, // Disable sourcemaps for production
    minify: 'esbuild', // Use esbuild instead of terser (faster and no extra dependency)
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          icons: ['lucide-react']
        }
      }
    }
  },

  // Server configuration for development
  server: {
    port: 5173,
    host: true,
    historyApiFallback: true // Important for SPA routing
  },

  // Preview configuration
  preview: {
    port: 4173,
    host: true,
    historyApiFallback: true // Important for SPA routing
  },

  // Base URL configuration
  base: '/',

  // Environment variables
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
  }
})
