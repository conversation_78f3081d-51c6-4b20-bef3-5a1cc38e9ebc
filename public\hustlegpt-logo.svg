<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Logo Icon -->
  <g>
    <!-- Background Circle -->
    <circle cx="20" cy="20" r="18" fill="url(#logoGradient)" />
    
    <!-- AI Brain/Circuit Pattern -->
    <g stroke="#ffffff" stroke-width="1.2" fill="none" opacity="0.9">
      <!-- Central Hub -->
      <circle cx="20" cy="20" r="2.5" fill="#ffffff" />
      
      <!-- Neural Network Lines -->
      <path d="M20 17.5 L17 13 M20 17.5 L23 13 M20 22.5 L17 27 M20 22.5 L23 27" stroke-linecap="round" />
      <path d="M17.5 20 L13 17 M17.5 20 L13 23 M22.5 20 L27 17 M22.5 20 L27 23" stroke-linecap="round" />
      
      <!-- Connection Nodes -->
      <circle cx="17" cy="13" r="1" fill="#ffffff" />
      <circle cx="23" cy="13" r="1" fill="#ffffff" />
      <circle cx="17" cy="27" r="1" fill="#ffffff" />
      <circle cx="23" cy="27" r="1" fill="#ffffff" />
      <circle cx="13" cy="17" r="1" fill="#ffffff" />
      <circle cx="13" cy="23" r="1" fill="#ffffff" />
      <circle cx="27" cy="17" r="1" fill="#ffffff" />
      <circle cx="27" cy="23" r="1" fill="#ffffff" />
    </g>
    
    <!-- Dollar Sign Overlay -->
    <g fill="#ffffff" opacity="0.8">
      <path d="M19 14 L19 12.5 L21 12.5 L21 14 M19 26 L19 27.5 L21 27.5 L21 26" stroke="#ffffff" stroke-width="0.8" />
      <path d="M17.5 16 C17.5 15.2 18.2 14.5 20 14.5 C21.8 14.5 22.5 15.2 22.5 16 C22.5 16.8 21.8 17 20 17.5 C18.2 18 17.5 18.2 17.5 19 C17.5 19.8 18.2 20.5 20 20.5 C21.8 20.5 22.5 19.8 22.5 19" 
            stroke="#ffffff" stroke-width="1.2" fill="none" stroke-linecap="round" />
    </g>
  </g>
  
  <!-- Text Logo -->
  <g fill="#1f2937">
    <!-- "Hustle" Text -->
    <text x="45" y="18" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2563eb">Hustle</text>
    
    <!-- "GPT" Text -->
    <text x="45" y="32" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7c3aed">GPT</text>
  </g>
  
  <!-- Alternative Text Version -->
  <g fill="#1f2937" font-family="Arial, sans-serif">
    <text x="45" y="25" font-size="16" font-weight="bold">
      <tspan fill="#2563eb">Hustle</tspan><tspan fill="#7c3aed">GPT</tspan>
    </text>
  </g>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
