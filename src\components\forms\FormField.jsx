import React from 'react';
import { Input, Textarea } from '../ui';
import clsx from 'clsx';

const FormField = ({
  name,
  label,
  type = 'text',
  placeholder,
  required = false,
  error,
  helperText,
  register,
  multiline = false,
  rows = 4,
  leftIcon,
  rightIcon,
  showPasswordToggle = false,
  className,
  ...props
}) => {
  const fieldProps = {
    label,
    type,
    placeholder,
    required,
    error,
    helperText,
    leftIcon,
    rightIcon,
    showPasswordToggle,
    className,
    ...props,
    ...(register && register(name))
  };

  if (multiline) {
    return (
      <Textarea
        rows={rows}
        {...fieldProps}
      />
    );
  }

  return (
    <Input
      {...fieldProps}
    />
  );
};

export default FormField;
