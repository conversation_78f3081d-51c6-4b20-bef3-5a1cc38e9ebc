@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for HustleGPT */
@layer base {
  html, body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    box-sizing: border-box;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
}

@layer components {
  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Gradient backgrounds */
  .gradient-bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-bg-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .gradient-bg-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  /* Image carousel animations */
  .animate-spin {
    animation: spin 1s linear;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse {
    animation: pulse 2s ease-in-out infinite;
  }

  .animate-image-transition {
    animation: imageTransition 1s ease-in-out;
  }

  /* Slow animations for floating cards */
  .animate-bounce-slow {
    animation: float 4s ease-in-out infinite;
    animation-delay: 0s;
  }

  .animate-pulse-slow {
    animation: float 5s ease-in-out infinite;
    animation-delay: 1s;
  }

  /* Blob animations for login page */
  .animate-blob {
    animation: blob 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  .animate-bounce-slow {
    animation: bounce-slow 3s infinite;
  }

  /* Slide animations for image carousel */
  .animate-slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
  }

  .animate-slide-out-left {
    animation: slideOutLeft 0.8s ease-in forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
  }

  .animate-slide-out-right {
    animation: slideOutRight 0.8s ease-in forwards;
  }

  .animate-fade-scale {
    animation: fadeScale 0.6s ease-in-out forwards;
  }

  /* Enhanced carousel transitions */
  .carousel-slide-smooth {
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .carousel-slide-fast {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Text overlay animations */
  .text-slide-up {
    animation: slideUp 0.6s ease-out 0.3s both;
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
}

@layer utilities {
  /* Remove default margins and padding */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Text gradient */
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Safe area padding for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 360-degree rotation animation for image carousel */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced slide animations with smooth transitions */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%) scale(0.9) rotateY(15deg);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1) rotateY(0deg);
    filter: blur(0px);
  }
}

@keyframes slideOutLeft {
  from {
    opacity: 1;
    transform: translateX(0) scale(1) rotateY(0deg);
    filter: blur(0px);
  }
  to {
    opacity: 0;
    transform: translateX(-100%) scale(0.9) rotateY(-15deg);
    filter: blur(2px);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%) scale(0.9) rotateY(-15deg);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1) rotateY(0deg);
    filter: blur(0px);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0) scale(1) rotateY(0deg);
    filter: blur(0px);
  }
  to {
    opacity: 0;
    transform: translateX(100%) scale(0.9) rotateY(15deg);
    filter: blur(2px);
  }
}

/* Smooth carousel transition */
@keyframes carouselSlide {
  0% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateX(-50%) scale(0.95);
    opacity: 0.7;
  }
  100% {
    transform: translateX(-100%) scale(0.9);
    opacity: 0;
  }
}

/* Fade and scale transition */
@keyframes fadeScale {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Floating animation for cards */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Pulse animation for indicators */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Blob animation for login page background */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

/* Slow bounce animation for demo badge */
@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Auto-rotating image animation */
@keyframes fade-in-out {
  0% {
    opacity: 0;
    transform: scale(1.05);
  }
  11.11% {
    opacity: 1;
    transform: scale(1);
  }
  33.33% {
    opacity: 1;
    transform: scale(1);
  }
  44.44% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 0;
    transform: scale(0.95);
  }
}

.animate-fade-in-out {
  animation: fade-in-out infinite;
  animation-fill-mode: both;
}

/* Additional floating animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(6deg);
  }
  50% {
    transform: translateY(-10px) rotate(6deg);
  }
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0px) rotate(-6deg);
  }
  50% {
    transform: translateY(-5px) rotate(-6deg);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    transform: scale(1) rotate(12deg) translateY(-50%);
  }
  50% {
    transform: scale(1.05) rotate(12deg) translateY(-50%);
  }
}

@keyframes spin-slow {
  0% {
    transform: rotate(45deg);
  }
  100% {
    transform: rotate(405deg);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounce-slow 2s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 2.5s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
