
import { Link } from 'react-router-dom';
import { ArrowR<PERSON>, TrendingUp, Users, BookOpen, Zap, Star, Clock, DollarSign, Target } from 'lucide-react';
import { Card, Button, Badge } from '../components/ui';
import { ROUTES } from '../constants/routes';
import { useAuth } from '../context/AuthContext';

const HomePage = () => {
  const { user } = useAuth();

  const quickActions = [
    {
      icon: Zap,
      title: 'Find My Hustle',
      description: 'Get AI-powered recommendations',
      link: ROUTES.AI_FINDER,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      icon: TrendingUp,
      title: 'Trending Now',
      description: 'See what\'s popular today',
      link: ROUTES.TRENDING,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      icon: BookOpen,
      title: 'Read Blog',
      description: 'Latest tips and guides',
      link: ROUTES.BLOG,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      icon: Users,
      title: 'Success Stories',
      description: 'Get inspired by others',
      link: ROUTES.SUCCESS_STORIES,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    }
  ];

  const recentActivity = [
    { action: 'Viewed', item: 'Freelance Writing', time: '2 hours ago' },
    { action: 'Saved', item: 'Online Tutoring', time: '1 day ago' },
    { action: 'Started', item: 'AI Finder Quiz', time: '3 days ago' }
  ];

  const recommendations = [
    {
      title: 'Freelance Writing',
      category: 'Writing',
      earnings: '$500-3000/month',
      difficulty: 'Beginner',
      match: 95
    },
    {
      title: 'Online Tutoring',
      category: 'Education',
      earnings: '$800-4000/month',
      difficulty: 'Intermediate',
      match: 88
    },
    {
      title: 'Social Media Management',
      category: 'Marketing',
      earnings: '$600-2500/month',
      difficulty: 'Intermediate',
      match: 82
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user?.name || 'Entrepreneur'}! 👋
          </h1>
          <p className="text-gray-600">
            Ready to continue building your side hustle empire?
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <Card.Content className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Target className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Hustles Tried</p>
                  <p className="text-2xl font-bold text-gray-900">{user?.hustlesTried || 0}</p>
                </div>
              </div>
            </Card.Content>
          </Card>

          <Card>
            <Card.Content className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <DollarSign className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Monthly Earnings</p>
                  <p className="text-2xl font-bold text-gray-900">$0</p>
                </div>
              </div>
            </Card.Content>
          </Card>

          <Card>
            <Card.Content className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Star className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Success Stories</p>
                  <p className="text-2xl font-bold text-gray-900">{user?.successStories || 0}</p>
                </div>
              </div>
            </Card.Content>
          </Card>

          <Card>
            <Card.Content className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Clock className="w-6 h-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Days Active</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {user?.joinedDate ? Math.floor((new Date() - new Date(user.joinedDate)) / (1000 * 60 * 60 * 24)) : 0}
                  </p>
                </div>
              </div>
            </Card.Content>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-2">
            <Card>
              <Card.Header>
                <Card.Title>Quick Actions</Card.Title>
                <Card.Description>Jump into your hustle journey</Card.Description>
              </Card.Header>
              <Card.Content>
                <div className="grid md:grid-cols-2 gap-4">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <Link key={index} to={action.link}>
                        <div className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
                          <div className="flex items-center mb-3">
                            <div className={`p-2 rounded-lg ${action.bgColor}`}>
                              <Icon className={`w-5 h-5 ${action.color}`} />
                            </div>
                            <h3 className="ml-3 font-semibold text-gray-900">{action.title}</h3>
                          </div>
                          <p className="text-sm text-gray-600">{action.description}</p>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              </Card.Content>
            </Card>

            {/* AI Recommendations */}
            <Card className="mt-6">
              <Card.Header>
                <Card.Title>Recommended for You</Card.Title>
                <Card.Description>AI-powered hustle suggestions based on your profile</Card.Description>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  {recommendations.map((rec, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <h4 className="font-semibold text-gray-900">{rec.title}</h4>
                          <Badge variant="primary" className="ml-2">{rec.match}% match</Badge>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>{rec.category}</span>
                          <span>•</span>
                          <span>{rec.earnings}</span>
                          <span>•</span>
                          <span>{rec.difficulty}</span>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        Learn More
                      </Button>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <Link to={ROUTES.AI_FINDER}>
                    <Button fullWidth rightIcon={<ArrowRight className="w-4 h-4" />}>
                      Get More Recommendations
                    </Button>
                  </Link>
                </div>
              </Card.Content>
            </Card>
          </div>

          {/* Sidebar */}
          <div>
            {/* Recent Activity */}
            <Card>
              <Card.Header>
                <Card.Title>Recent Activity</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      <div className="flex-1">
                        <p className="text-sm text-gray-900">
                          <span className="font-medium">{activity.action}</span> {activity.item}
                        </p>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </Card.Content>
            </Card>

            {/* Progress Card */}
            <Card className="mt-6">
              <Card.Header>
                <Card.Title>Your Progress</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Profile Completion</span>
                      <span>75%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Hustles Explored</span>
                      <span>3/10</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '30%' }}></div>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <Link to={ROUTES.DASHBOARD}>
                    <Button variant="outline" fullWidth size="sm">
                      View Full Dashboard
                    </Button>
                  </Link>
                </div>
              </Card.Content>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
