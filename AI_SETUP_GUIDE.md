# 🤖 AI Setup Guide for HustleGPT

## 🚨 **IMPORTANT: AI Not Working After Deployment?**

If you're seeing errors like:
- `POST https://openrouter.ai/api/v1/chat/completions 503 (Service Unavailable)`
- `OpenRouter API error: 503`
- `No instances available`

**This guide will fix the issue!**

---

## 🔧 **Step 1: Get Your OpenRouter API Key**

### **Create OpenRouter Account**
1. Go to [OpenRouter.ai](https://openrouter.ai)
2. Sign up for a free account
3. Verify your email address
4. Go to the API Keys section
5. Generate a new API key

### **Free Credits**
- OpenRouter gives you **free credits** to start
- No credit card required for basic usage
- Perfect for testing and small-scale usage

---

## 🌐 **Step 2: Configure Netlify Environment Variables**

### **In Netlify Dashboard:**
1. Go to your site dashboard
2. Click **Site Settings**
3. Click **Environment Variables** (in Build & Deploy section)
4. Click **Add Variable**

### **Required Variable:**
```
Key: VITE_AI_API_KEY
Value: your-openrouter-api-key-here
```

### **Optional Variables:**
```
Key: VITE_APP_URL
Value: https://your-site-name.netlify.app
```

---

## 🔄 **Step 3: Redeploy Your Site**

After adding environment variables:
1. Go to **Deploys** tab in Netlify
2. Click **Trigger Deploy** → **Deploy Site**
3. Wait for deployment to complete
4. Test the AI Finder page

---

## 🛠️ **Step 4: AI Model Fallback System**

Your system now includes **multiple AI model fallbacks**:

### **Primary Models (in order):**
1. `meta-llama/llama-3.1-8b-instruct:free` - Most reliable
2. `microsoft/phi-3-mini-128k-instruct:free` - Fast backup
3. `google/gemma-2-9b-it:free` - Google's model
4. `mistralai/mistral-7b-instruct:free` - Mistral backup
5. `nvidia/llama-3.3-nemotron-super-49b-v1:free` - Original (if available)

### **How It Works:**
- If one model is unavailable (503 error), system tries the next
- Automatic fallback ensures AI always works
- No user intervention required

---

## 🎯 **Step 5: Intelligent Mock Fallback**

If **all AI models fail**, the system provides:
- **Intelligent recommendations** based on user profile
- **Budget-aware filtering** 
- **Interest-based matching**
- **Experience-level appropriate suggestions**
- **Mock confidence scores** (75-95%)

**Users get recommendations either way!**

---

## 🔍 **Troubleshooting**

### **Issue: Still Getting 503 Errors**
**Solution:**
- Check API key is correctly set in Netlify
- Ensure key starts with `sk-or-v1-`
- Redeploy after setting variables

### **Issue: API Key Not Working**
**Solution:**
- Verify key is active in OpenRouter dashboard
- Check you have remaining credits
- Try generating a new key

### **Issue: No Recommendations Showing**
**Solution:**
- Check browser console for errors
- Verify environment variables are set
- System should show mock recommendations as fallback

### **Issue: Slow AI Responses**
**Solution:**
- Normal for free models (30-60 seconds)
- System has 60-second timeout
- Mock fallback activates if timeout exceeded

---

## 📊 **Testing Your Setup**

### **Test Steps:**
1. Go to AI Finder page
2. Complete the 6-step questionnaire
3. Click "Get AI Recommendations"
4. Check browser console for logs

### **Success Indicators:**
- ✅ `🤖 Trying AI model: meta-llama/llama-3.1-8b-instruct:free`
- ✅ `✅ Success with model: meta-llama/llama-3.1-8b-instruct:free`
- ✅ Recommendations appear with confidence scores

### **Fallback Indicators:**
- ⚠️ `❌ Model [name] failed: 503`
- ⚠️ `🎯 Generating intelligent mock recommendations`
- ✅ Mock recommendations with "AI Generated" badge

---

## 💡 **Pro Tips**

### **For Better AI Performance:**
- Use during off-peak hours (less traffic)
- Complete all questionnaire steps thoroughly
- Provide detailed interests and skills

### **For Production Use:**
- Consider upgrading to paid OpenRouter plan
- Monitor usage in OpenRouter dashboard
- Set up usage alerts

### **For Development:**
- Test with different user profiles
- Monitor console logs for errors
- Verify fallback systems work

---

## 🚀 **Expected Behavior**

### **Normal Flow:**
1. User completes questionnaire
2. System tries primary AI model
3. AI generates personalized recommendations
4. Results display with confidence scores

### **Fallback Flow:**
1. User completes questionnaire
2. AI models unavailable (503 errors)
3. System generates intelligent mock recommendations
4. Results display with "AI Generated" indicator

### **Both flows provide valuable recommendations!**

---

## 📞 **Support**

### **If Issues Persist:**
1. Check OpenRouter status page
2. Verify API key permissions
3. Test with different browsers
4. Check Netlify function logs

### **OpenRouter Resources:**
- **Documentation**: [openrouter.ai/docs](https://openrouter.ai/docs)
- **Status Page**: [status.openrouter.ai](https://status.openrouter.ai)
- **Discord**: OpenRouter community support

---

## ✅ **Quick Checklist**

- [ ] OpenRouter account created
- [ ] API key generated
- [ ] `VITE_AI_API_KEY` set in Netlify
- [ ] Site redeployed after setting variables
- [ ] AI Finder tested and working
- [ ] Fallback system verified

**Your AI-powered HustleGPT is now ready to help users find their perfect side hustles!** 🎉
